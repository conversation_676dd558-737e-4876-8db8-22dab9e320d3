NODE_ENV=production
PORT=5100

# MongoDB connection string (use your own credentials)
MONGO_URL=mongodb://localhost:27017/sukoonsphere

# Security (generate your own secrets)
JWT_SECRET=your_jwt_secret_here
COOKIE_SECRET=your_cookie_secret_here
JWT_EXPIRES_IN=1d

BACKEND_URL=https://your-domain.com

REQUEST_CONTRIBUTER_SECRET=your_request_contributor_secret_here

# Cloudinary configuration
CLOUD_API_KEY=your_cloud_api_key
CLOUD_API_SECRET=your_cloud_api_secret
CLOUD_NAME=your_cloud_name

# Gemini API Key
GEMINI_API_KEY=your_gemini_api_key

# Deepgram API Key for Text-to-Speech
DEEPGRAM_API_KEY=your_deepgram_api_key

# Email Configuration
EMAIL_HOST=smtp-relay.brevo.com
EMAIL_PORT=587
EMAIL_USER=your_email_user
EMAIL_PASS=your_email_password
EMAIL_FROM=<EMAIL>

# Social Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

TWITTER_CONSUMER_KEY=your_twitter_consumer_key
TWITTER_CONSUMER_SECRET=your_twitter_consumer_secret
