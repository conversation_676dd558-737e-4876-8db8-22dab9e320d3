server {
    listen 80;
    listen [::]:80;
    server_name learn.sukoonsphere.org;

    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

# SSL configuration
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name learn.sukoonsphere.org;

    # SSL certificates
    ssl_certificate /etc/letsencrypt/live/learn.sukoonsphere.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/learn.sukoonsphere.org/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy strict-origin-when-cross-origin;

    # Proxy settings - Assuming the learn application will run on port 8020
    # Update this to the actual port when the learn application is deployed
    location / {
        # For now, redirect to the main site with a message
        return 302 https://www.sukoonsphere.org/learn;

        # When the learn application is ready, uncomment and update the following:
        # proxy_pass http://127.0.0.1:8020;
        # proxy_http_version 1.1;
        # proxy_set_header Upgrade $http_upgrade;
        # proxy_set_header Connection 'upgrade';
        # proxy_set_header Host $host;
        # proxy_set_header X-Real-IP $remote_addr;
        # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_set_header X-Forwarded-Proto $scheme;
        # proxy_cache_bypass $http_upgrade;
    }
}
