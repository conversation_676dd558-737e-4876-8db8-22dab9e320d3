node:internal/modules/cjs/loader:1252
  throw err;
  ^

Error: Cannot find module '/root/SukoonSphere.org/client/pnpm'
    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)
    at Function._load (node:internal/modules/cjs/loader:1075:27)
    at TracingChannel.traceSync (node:diagnostics_channel:315:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49 {
  code: 'MODULE_NOT_FOUND',
  requireStack: []
}

Node.js v22.11.0

> client@0.0.0 preview /root/Working_Directory/SukoonSphere/client
> vite preview "--host"

  ➜  Local:   http://localhost:4173/
  ➜  Network: http://***************:4173/
node:internal/modules/cjs/loader:1252
  throw err;
  ^

Error: Cannot find module '/root/Working_Directory/SukoonSphere/client/server.js'
    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)
    at Function._load (node:internal/modules/cjs/loader:1075:27)
    at TracingChannel.traceSync (node:diagnostics_channel:315:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49 {
  code: 'MODULE_NOT_FOUND',
  requireStack: []
}

Node.js v22.11.0
