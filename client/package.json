{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@ckeditor/ckeditor5-build-classic": "^43.3.1", "@deepgram/sdk": "^3.12.1", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-slot": "^1.1.0", "@react-pdf/renderer": "^4.1.4", "@reduxjs/toolkit": "^2.3.0", "@splidejs/react-splide": "^0.7.12", "@splidejs/splide-extension-auto-scroll": "^0.5.3", "@tanstack/react-query": "^5.62.2", "@tinymce/tinymce-react": "^5.1.1", "axios": "^1.3.6", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "cloudinary": "^2.4.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "emoji-picker-react": "^4.12.0", "framer-motion": "^12.6.3", "highcharts": "^12.1.2", "highcharts-react-official": "^3.2.1", "html2canvas": "^1.4.1", "jodit-react": "^4.1.2", "lucide-react": "^0.487.0", "quill": "^2.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.13.1", "react-markdown": "^10.1.0", "react-pageflip": "^2.0.3", "react-pdf": "^9.1.1", "react-quill": "^0.0.2", "react-redux": "^9.1.2", "react-router-dom": "^6.10.0", "react-select": "^5.8.1", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^10.0.6", "recharts": "^2.15.1", "redux": "^5.0.1", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "styled-components": "^5.3.10", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-sound": "^4.0.3"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.14", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "daisyui": "^4.12.10", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.10", "vite": "^6.2.3"}}