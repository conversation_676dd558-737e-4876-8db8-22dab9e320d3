/* TherapySessionsListPage.css */

.therapy-sessions-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.therapy-sessions-header {
  background-color: #f0f9ff;
  border-radius: 16px;
  padding: 40px;
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.therapy-sessions-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0) 70%);
  z-index: 0;
}

.header-content {
  position: relative;
  z-index: 1;
}

.therapy-sessions-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #0f172a;
  margin: 0 0 16px 0;
}

.therapy-sessions-header p {
  font-size: 1.125rem;
  line-height: 1.6;
  color: #334155;
  margin: 0 0 24px 0;
  max-width: 600px;
}

.new-session-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.new-session-button:hover:not(:disabled) {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.new-session-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Stats section */
.therapy-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background-color: #eff6ff;
  color: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
}

/* Sessions list */
.therapy-sessions-list {
  margin-bottom: 60px;
}

.therapy-sessions-list h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #0f172a;
  margin: 0 0 24px 0;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: #64748b;
  font-size: 1rem;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.no-sessions {
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  color: #64748b;
}

.no-sessions p {
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.no-sessions p:last-child {
  margin-bottom: 0;
}

.sessions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.session-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.session-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.session-card.active {
  border-left: 4px solid #3b82f6;
}

.session-card.completed {
  border-left: 4px solid #10b981;
}

.session-card.archived {
  border-left: 4px solid #94a3b8;
  opacity: 0.8;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.session-stage {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #64748b;
  background-color: #f1f5f9;
  padding: 4px 12px;
  border-radius: 12px;
}

.session-status {
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  color: #64748b;
}

.session-card.active .session-status {
  color: #3b82f6;
}

.session-card.completed .session-status {
  color: #10b981;
}

.session-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 16px;
  line-height: 1.4;
}

.session-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.session-messages,
.session-date {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #64748b;
}

.session-summary {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
  margin-bottom: 16px;
}

.summary-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #10b981;
}

.session-continue {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
  font-size: 0.875rem;
  font-weight: 500;
  color: #3b82f6;
}

/* Therapy info section */
.therapy-info {
  background-color: #f8fafc;
  border-radius: 16px;
  padding: 40px;
}

.therapy-info h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #0f172a;
  margin: 0 0 24px 0;
}

.therapy-stages {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stage-item {
  display: flex;
  gap: 16px;
}

.stage-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background-color: #eff6ff;
  color: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.stage-content h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #0f172a;
  margin: 0 0 8px 0;
}

.stage-content p {
  font-size: 0.875rem;
  line-height: 1.6;
  color: #475569;
  margin: 0;
}

.therapy-disclaimer {
  background-color: #fff1f2;
  border-radius: 12px;
  padding: 24px;
  border-left: 4px solid #f43f5e;
}

.therapy-disclaimer h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #be123c;
  margin: 0 0 12px 0;
}

.therapy-disclaimer p {
  font-size: 0.875rem;
  line-height: 1.6;
  color: #9f1239;
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .therapy-sessions-header {
    padding: 30px 20px;
  }

  .therapy-sessions-header h1 {
    font-size: 2rem;
  }

  .therapy-sessions-header p {
    font-size: 1rem;
  }

  .therapy-info {
    padding: 30px 20px;
  }
}
