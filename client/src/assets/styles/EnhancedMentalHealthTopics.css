.enhanced-mental-health-topics {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem .3rem;
}

.categories-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.category-section {
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  background-color: white;
}

.category-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.category-row:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 0 0 25%;
}

.category-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.category-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--grey--900);
}

.category-disorders {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
  justify-content: center;
}

.disorder-tag {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  background-color: #f3f4f6;
  border-radius: 9999px;
  white-space: nowrap;
  color: var(--grey--700);
}

.category-toggle {
  flex-shrink: 0;
  width: 2rem;
  display: flex;
  justify-content: center;
}

.toggle-icon {
  transition: transform 0.3s ease;
}

.toggle-icon.rotate-90 {
  transform: rotate(90deg);
}

.category-details {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  overflow: hidden;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
}

.details-image {
  height: 100%;
  overflow: hidden;
  border-radius: 0.5rem;
}

.details-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.5rem;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.details-description h4,
.details-symptoms h4,
.details-resources h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--grey--900);
  margin-bottom: 0.5rem;
}

.details-description p {
  color: var(--grey--700);
  line-height: 1.6;
}

.details-symptoms ul {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.5rem;
  list-style: none;
  padding: 0;
}

.details-symptoms li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--grey--700);
}

.symptom-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.details-resources ul {
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.resource-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.resource-link:hover {
  opacity: 0.8;
}

.resource-icon {
  font-size: 0.75rem;
}

.symptom-links {
  margin-top: 1rem;
  border-top: 1px dashed #e5e7eb;
  padding-top: 1rem;
}

.symptom-pdf-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  font-weight: 500;
  transition: opacity 0.2s ease;
  padding: 0.5rem;
  border-radius: 0.25rem;
  background-color: rgba(0, 0, 0, 0.03);
}

.symptom-pdf-link:hover {
  opacity: 0.8;
  background-color: rgba(0, 0, 0, 0.05);
}

.details-footer {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.learn-more-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  color: white;
  font-weight: 500;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.learn-more-link:hover {
  opacity: 0.9;
}

.topics-footer {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.disclaimer {
  font-size: 0.875rem;
  color: var(--grey--600);
  max-width: 600px;
}

.all-resources-link {
  display: flex;
  align-items: center;
  color: var(--primary);
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s ease;
}

.all-resources-link:hover {
  color: var(--primary-dark);
}

/* Responsive styles */
@media (max-width: 768px) {
  .details-grid {
    grid-template-columns: 1fr;
  }

  .details-image {
    max-height: 200px;
  }

  .category-header {
    flex: 0 0 auto;
  }

  .category-disorders {
    display: none;
  }

  .category-row {
    padding: 0.75rem 1rem;
  }

  .category-name {
    font-size: 1rem;
  }

  .details-symptoms ul {
    grid-template-columns: 1fr;
  }
}