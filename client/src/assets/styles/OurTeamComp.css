.v2-included_features-list {
    color: var(--grey);
    margin-bottom: 20px;
}

.v2-expertise_grid {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    grid-template-rows: auto;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-auto-columns: 1fr;
    place-items: stretch center;
    width: 100%;
    max-width: 1016px;
    margin-left: auto;
    margin-right: auto;
    display: grid;
}

.v2-expertise_grid-item {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    text-align: center;
    flex-flow: column;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 40px;
    display: flex;
}

.v2-expertise_grid-icon_wrapper {
    background-image: linear-gradient(64.08deg, #7a57f11f 9.66%, #7a57f100 100.73%), linear-gradient(0deg, #111112, #111112);
    border: 1px solid #7a57f133;
    border-radius: 12px;
    justify-content: center;
    align-items: center;
    width: 72px;
    height: 72px;
    display: flex;
    box-shadow: 0 1.33px 64px 5.33px #9281ef14;
}

.v2-expertise_grid-icon {
    width: 40px;
    height: 40px;
}

.v2-composition_wrapper {
    justify-content: center;
    align-items: center;
    display: flex;
}

.v2-composition_img {
    border-radius: 24px;
    flex: none;
    position: relative;
    box-shadow: 0 4px 220px 16px #9281ef29;
}

.v2-composition_img.center-img {
    top: -80px;
}

.v2-composition_img.left-img {
    right: -44px;
}

.v2-composition_img.right-img {
    left: -44px;
}

.section-dedication {
    padding-top: 288px;
    padding-bottom: 0;
}

.v2-get_started-white-cards_flex_wrapper-copy {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    display: flex;
}

.v2-3col-cards_flex_wrapper {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    flex-flow: row;
    justify-content: space-between;
    display: flex;
}

.v2-3col-black_card {
    border: 1px solid var(--black-border);
    background-color: #111112;
    border-radius: 24px;
    width: 100%;
    max-width: 33.33%;
    padding: 32px 32px 40px;
}

.v2-3col-black_card-icon_wrapper {
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    margin-bottom: 16px;
    display: flex;
}

.mb-16 {
    margin-bottom: 16px;
}

.v2-included_features_check {
    width: 24px;
}

.v2-included_features-accordion_heading-hflex {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    justify-content: flex-start;
    align-items: flex-start;
    max-width: 90%;
    display: inline-flex;
}

.v2-included_features-accordion_heading {
    color: #fff;
    white-space: normal;
    margin-top: 0;
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
}

.v2-home_hero {
    margin-top: -80px;
    padding-top: 160px;
    position: relative;
}

.v2-home_hero-banner {
    background-image: linear-gradient(64.08deg, #7a57f11f 9.66%, #7a57f100 100.73%), linear-gradient(0deg, #111112, #111112);
    border: 1px solid #7a57f133;
    border-radius: 24px;
    margin-top: 32px;
    padding: 32px;
}

.v2-home_hero-banner-hflex_wrapper {
    justify-content: space-between;
    align-items: center;
    display: flex;
}

.marquee {
    height: 31px;
    overflow: hidden;
}

.marquee_track {
    width: 100%;
    height: 100%;
    position: relative;
}

.marquee_item {
    width: 100%;
    height: 100%;
}

.v2-home_hero-image-desktop {
    object-fit: contain;
    border-radius: 24px;
    width: 100%;
    box-shadow: 0 4px 220px 44px #9281ef29;
}

.v2-home_hero-image_wrapper {
    margin-top: -50px;
    position: relative;
}

.v2-home_hero-heading_wrapper {
    z-index: 2;
    position: relative;
}

.v2-home_hero-floating-asset_1 {
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    background-image: linear-gradient(68.7deg, #13101f80, #09081080);
    border-radius: 12px;
    position: absolute;
    top: 35px;
    left: -5%;
    box-shadow: 0 20px 48px #00000029;
}

.v2-composition_wrapper {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.v2-composition_img {
    border-radius: 10px;
}

.text_align-center {
    text-align: center;
}

.heading_2 {
    font-size: 2.5rem;
    font-weight: bold;
    color: #ffffff;
}

.paragraph_1 {
    font-size: 1.125rem;
    color: #999999;
}

.mt-80 {
    margin-top: 80px;
}

.mt-24 {
    margin-top: 24px;
}