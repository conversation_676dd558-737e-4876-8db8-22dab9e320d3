/* ChatbotAttention.css */

.chatbot-attention-ring {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 188, 212, 0.2) 0%, rgba(0, 188, 212, 0) 70%);
  z-index: 999;
  pointer-events: none;
  animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
}

.chatbot-attention-pointer {
  position: fixed;
  bottom: 90px;
  right: 40px;
  z-index: 999;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.3));
  animation: bounce 2s ease infinite;
}

.chatbot-attention-tooltip {
  position: fixed;
  bottom: 130px;
  right: 20px;
  width: 250px;
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  z-index: 999;
  text-align: center;
}

.chatbot-attention-tooltip p {
  margin: 0 0 10px;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
}

.chatbot-attention-tooltip p:first-child {
  font-weight: bold;
  font-size: 16px;
  color: #00bcd4;
}

.tooltip-button {
  background-color: #00bcd4;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 8px;
  transition: all 0.2s ease;
}

.tooltip-button:hover {
  background-color: #0097a7;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .chatbot-attention-tooltip {
    width: 200px;
    right: 10px;
    bottom: 110px;
  }
  
  .chatbot-attention-pointer {
    right: 30px;
  }
}
