Comment Section is deprecated since its only for one time use
// This componenent is deprecated since its only for one time use


import React, { useEffect, useState } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import customFetch from '@/utils/customFetch';
import Comment from './Comment';
import ContentEditor from '../shared/ContentEditor';

const CommentSection = ({ postId }) => {
  const { user, isAuthenticated } = useAuth0();
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchComments = async () => {
    try {
      setIsLoading(true);
      const { data } = await customFetch.get(`/posts/${postId}/comments`);
      setComments(data || []);
    } catch (error) {
      setError('Failed to load comments');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddComment = async (e) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    try {
      setIsLoading(true);
      const { data } = await customFetch.post(`/posts/${postId}/comments`, {
        content: newComment,
        postId: postId,
        username: user?.name || 'Anonymous',
        userAvatar: user?.picture || null
      });

      if (data.comment) {
        setComments(prevComments => [data.comment, ...prevComments]);
      }
      setNewComment('');
    } catch (error) {
      setError('Please login to comment');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteComment = async (commentId) => {
    try {
      await customFetch.delete(`/posts/comments/${commentId}`);
      setComments(prevComments =>
        prevComments.filter(comment => comment._id !== commentId)
      );
    } catch (error) {
      setError('Failed to delete comment');
      console.error(error);
    }
  };

  const handleEditComment = async (commentId, newContent) => {
    try {
      const { data } = await customFetch.put(`/posts/${postId}/comments/${commentId}`, {
        content: newContent
      });

      if (data.comment) {
        setComments(prevComments =>
          prevComments.map(comment =>
            comment._id === commentId ? { ...comment, content: newContent } : comment
          )
        );
      }
    } catch (error) {
      setError('Failed to edit comment');
      console.error(error);
    }
  };

  useEffect(() => {
    fetchComments();
  }, [postId]);

  return (
    <div className="mt-4">
      {error && (
        <div className="bg-red-100 text-red-700 p-2 sm:p-3 rounded-md mb-4 text-sm">
          {error}
        </div>
      )}

      {true ? (
        <form onSubmit={handleAddComment} className="mb-4">
          <ContentEditor
            content={newComment}
            setContent={setNewComment}
            onSave={handleAddComment}
            onCancel={() => setNewComment('')}
            isLoading={isLoading}
            type="comment"
            buttonSize="sm"
          />
        </form>
      ) : (
        <p className="text-center text-gray-500 my-4 text-sm">
          Please sign in to comment
        </p>
      )}

      {isLoading && comments.length === 0 && (
        <div className="text-center text-gray-500 my-4">
          Loading comments...
        </div>
      )}

      <div className="space-y-2 sm:space-y-4">
        {comments.map(comment => (
          <Comment
            key={comment._id}
            comment={comment}
            postId={postId}
            onDelete={handleDeleteComment}
            onEdit={handleEditComment}
            isAuthor={isAuthenticated && user?.sub === comment.createdBy}
          />
        ))}
      </div>
    </div>
  );
};

export default CommentSection;




// import React, { useEffect, useState } from 'react';
// import { FaEdit, FaTrash, FaReply, FaThumbsUp, FaThumbsDown } from 'react-icons/fa';
// import { BsThreeDots } from 'react-icons/bs';
// import Reply from './Reply';
// import { useAuth0 } from '@auth0/auth0-react';
// import customFetch from '@/utils/customFetch';
// import UserAvatar from '../shared/UserAvatar';
// import ActionButtons from '../shared/ActionButtons';
// import ContentEditor from '../shared/ContentEditor';
// import ReactionButtons from '../shared/ReactionButtons';
// import { useUser } from '@/context/UserContext';

// const Comment = ({ comment, onDelete, onEdit, postId }) => {
//     const { user } = useUser();
//     const isAuthor = user?._id === comment.createdBy;
//     console.log({ comment })
//     const { isAuthenticated } = useAuth0();
//     const [isEditing, setIsEditing] = useState(false);
//     const [editedContent, setEditedContent] = useState(comment.content);
//     const [showReplyForm, setShowReplyForm] = useState(false);
//     const [replyContent, setReplyContent] = useState('');
//     const [replies, setReplies] = useState(comment.replies || []);
//     const [showReplies, setShowReplies] = useState(false);
//     const [isLoading, setIsLoading] = useState(false);
//     const [likes, setLikes] = useState(comment.likes || 0);
//     const [dislikes, setDislikes] = useState(comment.dislikes || 0);
//     const [userReaction, setUserReaction] = useState(comment.userReaction || null);

//     const handleReaction = async (type) => {
//         if (!isAuthenticated) return;

//         try {
//             const { data } = await customFetch.post(`/posts/${postId}/comments/${comment._id}/reaction`, {
//                 type,
//                 userId: user.sub
//             });

//             setLikes(data.likes);
//             setDislikes(data.dislikes);
//             setUserReaction(data.userReaction);
//         } catch (error) {
//             console.error('Failed to update reaction:', error);
//         }
//     };

//     const handleEdit = async () => {
//         try {
//             await onEdit(comment._id, editedContent);
//             setIsEditing(false);
//         } catch (error) {
//             console.error('Failed to edit comment:', error);
//         }
//     };

//     const handleAddReply = async (e) => {
//         // e.preventDefault();
//         if (!replyContent.trim()) return;

//         try {
//             setIsLoading(true);
//             const { data } = await customFetch.post(`posts/comments/${comment._id}/replies`, {
//                 content: replyContent,
//             });
//             console.log({ datareply: data });

//             if (data.reply) {
//                 setReplies(prevReplies => [data.reply, ...prevReplies]);
//                 setReplyContent('');
//                 setShowReplyForm(false);
//             }
//         } catch (error) {
//             console.error('Failed to add reply:', error);
//         } finally {
//             setIsLoading(false);
//         }
//     };
//     const fetchReplies = async () => {
//         const { data } = await customFetch.get(`posts/comments/${comment._id}/replies`);
//         console.log({ data });
//         setReplies(data.replies);
//     };
//     useEffect(() => {
//         fetchReplies();
//     }, []);

//     const handleDeleteReply = async (replyId) => {
//         try {
//             await customFetch.delete(`posts/comments/replies/${replyId}`);
//             setReplies(prevReplies => prevReplies.filter(reply => reply._id !== replyId));
//         } catch (error) {
//             console.error('Failed to delete reply:', error);
//         }
//     };

//     const handleEditReply = async (replyId, newContent) => {
//         try {
//             const { data } = await customFetch.put(
//                 `posts/comments/${comment._id}/replies/${replyId}`,
//                 { content: newContent }
//             );

//             if (data.reply) {
//                 setReplies(prevReplies =>
//                     prevReplies.map(reply =>
//                         reply._id === replyId ? { ...reply, content: newContent } : reply
//                     )
//                 );
//             }
//         } catch (error) {
//             console.error('Failed to edit reply:', error);
//         }
//     };

//     return (
//         <div className="px-4">
//             <div className="flex gap-3">
//                 <UserAvatar user={comment} size="medium" />

//                 <div className="flex-grow">
//                     {/* Comment Header */}
//                     <div className="flex items-start justify-start">
//                         <div className="flex items-start justify-between w-full">
//                             <div>
//                                 <h4 className="font-semibold text-sm">{comment.username}</h4>
//                                 <p className="text-xs text-gray-500">
//                                     {new Date(comment.createdAt).toLocaleString()}
//                                     {comment.isEdited && ' (edited)'}
//                                 </p>
//                             </div>
//                             {isAuthor && (
//                                 <div className="ml-auto">
//                                     <button
//                                         onClick={() => onDelete(comment._id)}
//                                         className="text-red-500 hover:text-red-600 transition-colors"
//                                     >
//                                         <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
//                                             <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
//                                         </svg>
//                                     </button>
//                                 </div>
//                             )}
//                         </div>
//                     </div>

//                     {/* Comment Body */}
//                     {isEditing ? (
//                         <ContentEditor
//                             content={editedContent}
//                             setContent={setEditedContent}
//                             onSave={handleEdit}
//                             onCancel={() => setIsEditing(false)}
//                             isLoading={isLoading}
//                         />
//                     ) : (
//                         <>
//                             <p className="mt-1 text-sm text-gray-800">{comment.content}</p>
//                             <ReactionButtons
//                                 likes={likes}
//                                 dislikes={dislikes}
//                                 userReaction={userReaction}
//                                 onLike={handleReaction}
//                                 onDislike={handleReaction}
//                                 onReply={() => setShowReplyForm(!showReplyForm)}
//                             />
//                         </>
//                     )}

//                     {/* Reply Form */}
//                     {showReplyForm && (
//                         <ContentEditor
//                             content={replyContent}
//                             setContent={setReplyContent}
//                             onSave={handleAddReply}
//                             onCancel={() => setShowReplyForm(false)}
//                             isLoading={isLoading}
//                             buttonSize="xs"
//                             type="reply"
//                         />
//                     )}

//                     {/* Replies Section - Fixed */}
//                     {replies.length > 0 && (
//                         <div className="mt-3 pl-4 border-l-2 border-gray-100">
//                             <button
//                                 onClick={() => setShowReplies(!showReplies)}
//                                 className="text-sm text-blue-600 hover:text-blue-700 mb-2"
//                             >
//                                 {showReplies ? '▼' : '►'} {replies.length} {replies.length === 1 ? 'reply' : 'replies'}
//                             </button>

//                             {showReplies && (
//                                 <div className="space-y-3">
//                                     {replies.map(reply => (
//                                         <Reply
//                                             key={reply._id}
//                                             reply={reply}
//                                             onDelete={handleDeleteReply}
//                                             onEdit={handleEditReply}
//                                             isAuthor={isAuthenticated && user?.sub === reply.createdBy}
//                                             postId={postId}
//                                             commentId={comment._id}
//                                         />
//                                     ))}
//                                 </div>
//                             )}
//                         </div>
//                     )}
//                 </div>
//             </div>
//         </div>
//     );
// };

// export default Comment;





Functional PostCard



import React, { useState, useCallback, useEffect, lazy, Suspense } from 'react';
import { BsPersonDash, BsPersonPlus, BsThreeDotsVertical } from 'react-icons/bs';

import UserAvatar from '@/components/shared/UserAvatar';
import customFetch from '@/utils/customFetch';
import { useUser } from '@/context/UserContext';
import { AiOutlineComment } from 'react-icons/ai';
import { Follow, Like } from '@/components';
// Lazy load components that aren't immediately needed
const CommentSection = lazy(() => import('@/components/shared/Comments/CommentSection'));
const DeleteModal = lazy(() => import('@/components/shared/DeleteModal'));

/**
 * PostCard Component
 * Displays a single post with user interactions like comments, likes, and follow/unfollow functionality
 * 
 * @param {Object} post - The post object containing all post data
 * @param {Function} onPostDelete - Callback function executed when a post is deleted
 */
const PostCard = ({ post, onPostDelete }) => {
    const [showComments, setShowComments] = useState(false);
    const [showActionModal, setShowActionModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [isFollowing, setIsFollowing] = useState(false);
    const [comments, setComments] = useState([]);
    const { user } = useUser();
    const isAuthor = user?._id === post.createdBy;

    const handleCommentError = useCallback((error) => {
        console.error(error);
    }, []);

    const fetchComments = useCallback(async () => {
        try {
            const { data } = await customFetch.get(`/posts/${post._id}/comments`);
            setComments(data || []);
        } catch (error) {
            handleCommentError(error?.response?.data?.msg || 'Failed to fetch comments');
            throw error;
        }
    }, [post._id]);

    // Only fetch comments when they're shown
    useEffect(() => {
        if (showComments) {
            fetchComments();
        }
    }, [fetchComments, showComments]);

    const handleAddComment = useCallback(async (content) => {
        try {
            const { data } = await customFetch.post(`/posts/${post._id}/comments`, { content });
            setComments(prev => [data.comment, ...prev]); // Optimistic update
            setShowComments(true);
            return data.comment;
        } catch (error) {
            handleCommentError(error?.response?.data?.msg || 'Failed to add comment');
            throw error;
        }
    }, [post._id]);

    const handleDeleteComment = useCallback(async (commentId) => {
        try {
            await customFetch.delete(`/posts/comments/${commentId}`);
            setComments(prev => prev.filter(comment => comment._id !== commentId));
        } catch (error) {
            handleCommentError(error?.response?.data?.msg || 'Failed to delete comment');
            throw error;
        }
    }, []);

    const handleReplyToComment = useCallback(async (commentId, content) => {
        try {
            const { data } = await customFetch.post(`/posts/comments/${commentId}/replies`, { content });
            setComments(prev => prev.map(comment => {
                if (comment._id === commentId) {
                    return {
                        ...comment,
                        replies: [...(comment.replies || []), data.reply]
                    };
                }
                return comment;
            }));
            return data.reply;
        } catch (error) {
            handleCommentError(error?.response?.data?.msg || 'Failed to add reply');
            throw error;
        }
    }, []);

    const handleDeleteReply = useCallback(async (replyId) => {
        try {
            await customFetch.delete(`/posts/comments/replies/${replyId}`);
            setComments(prev => prev.map(comment => ({
                ...comment,
                replies: (comment.replies || []).filter(reply => reply._id !== replyId)
            })));
        } catch (error) {
            handleCommentError(error?.response?.data?.msg || 'Failed to delete reply');
            throw error;
        }
    }, []);

    const handleDelete = useCallback(async () => {
        try {
            setIsDeleting(true);
            await customFetch.delete(`/posts/${post._id}`);
            setShowDeleteModal(false);
            onPostDelete?.(post._id);
        } catch (error) {
            handleCommentError('Error deleting post');
        } finally {
            setIsDeleting(false);
        }
    }, [post._id, onPostDelete]);

    const handleFollowOrUnfollow = useCallback(async () => {
        try {
            await customFetch.patch(`/user/follow/${post.createdBy}`);
            setIsFollowing(prev => !prev);
        } catch (error) {
            handleCommentError('Error following/unfollowing user');
        }
    }, [post.createdBy]);

    const handleLikePost = async () => {
        try {
            await customFetch.patch(`/posts/${post._id}/like`);
        } catch (error) {
            handleCommentError('Error liking post');
        }
    };

    const toggleComments = useCallback(() => {
        setShowComments(prev => !prev);
    }, []);

    return (
        <>
            <div className="mb-4 p-3 sm:p-4 border rounded-lg bg-[var(--white-color)]">
                <div className="flex items-center mb-4 justify-between flex-wrap gap-2">
                    <div className="flex items-center gap-2">
                        <UserAvatar
                            user={{
                                picture: post?.avatar || '',
                                username: post?.username || ''
                            }}
                            size="medium"
                        />
                        <div >
                            <h4 className="font-semibold text-sm sm:text-base">{post?.username || 'Anonymous'}</h4>
                            <p className="text-gray-500 text-xs sm:text-sm">
                                {post?.datePublished ? new Date(post?.datePublished).toLocaleDateString() : 'Date not available'}
                            </p>
                        </div>
                    </div>

                    {user && (
                        <div className="relative">
                            {!isAuthor && (
                                <Follow
                                    isFollowed={isFollowing}
                                    userId={post.createdBy}
                                    followers={post?.followers}
                                    onFollow={handleFollowOrUnfollow}
                                    onError={handleCommentError}
                                />
                            )}

                            {isAuthor && (
                                <>
                                    <BsThreeDotsVertical
                                        className="text-black cursor-pointer"
                                        onClick={() => setShowActionModal(!showActionModal)}
                                    />

                                    {showActionModal && (
                                        <div className="absolute right-0 mt-2 w-32 bg-white border rounded-lg shadow-lg  z-10">
                                            <button
                                                className="w-full px-4 py-2 text-left text-red-600 hover:bg-gray-100 rounded-lg"
                                                onClick={() => {
                                                    setShowDeleteModal(true);
                                                    setShowActionModal(false);
                                                }}
                                            >
                                                Delete Post
                                            </button>
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    )}
                </div>

                {post?.imageUrl && (
                    <div className="w-full h-[200px] sm:h-[300px] rounded-lg overflow-hidden mb-4">
                        <img
                            src={post.imageUrl}
                            alt="Post visual"
                            className="w-full h-full object-cover"
                            loading="lazy"
                        />
                    </div>
                )}
                <p className="mb-4 text-sm sm:text-base">{post?.description || 'No description available'}</p>

                <div className="mt-2 flex flex-wrap gap-2">
                    {post?.tags?.map((tag) => (
                        <span
                            key={tag}
                            className="inline-block bg-blue-200 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full"
                        >
                            #{tag}
                        </span>
                    ))}
                </div>

                <div className="flex justify-between text-gray-500 text-sm mt-4 flex-wrap gap-2">
                    <div className="flex items-center gap-2 sm:gap-4">
                        <Like
                            onLike={handleLikePost}
                            totalLikes={post?.totalLikes}
                            likes={post?.likes}
                            id={post?._id}
                            onError={handleCommentError}
                        />
                        <button
                            onClick={toggleComments}
                            className="flex items-center gap-1 hover:text-blue-500"
                        >
                            <AiOutlineComment className='w-5 h-5' />
                            <span className='text-sm font-medium text-[var(--grey--900)] hover:text-blue-500'>
                                {comments.length || 0} comments
                            </span>
                        </button>
                    </div>
                </div>

                {showComments && (
                    <Suspense fallback={<div className="mt-4 text-center">Loading comments...</div>}>
                        <div className="mt-4 border-t pt-4">
                            <CommentSection
                                comments={comments}
                                onAddComment={handleAddComment}
                                onDeleteComment={handleDeleteComment}
                                onReplyToComment={handleReplyToComment}
                                onDeleteReply={handleDeleteReply}
                                currentUser={user}
                                type="post"
                            />
                        </div>
                    </Suspense>
                )}
            </div>

            {showDeleteModal && (
                <Suspense fallback={<div>Loading...</div>}>
                    <DeleteModal
                        isOpen={showDeleteModal}
                        onClose={() => setShowDeleteModal(false)}
                        onDelete={handleDelete}
                        title="Delete Post"
                        message="Are you sure you want to delete this post?"
                        itemType="post"
                        isLoading={isDeleting}
                    />
                </Suspense>
            )}
        </>
    );
};

export default PostCard;
