/* FullScreenChatbot.css */

/* Base styles remain unchanged unless specified */
.fullscreen-chatbot {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  z-index: 9999;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Dark mode styles remain unchanged unless specified */
.fullscreen-chatbot.dark-mode {
  background-color: #1a1a1a;
  color: #e0e0e0;
}

.fullscreen-chatbot.dark-mode .chatbot-sidebar {
  background-color: #252525;
  border-right: 1px solid #333;
}

.fullscreen-chatbot.dark-mode .chatbot-header,
.fullscreen-chatbot.dark-mode .chat-input {
  background-color: #252525;
  border-color: #333;
}

.fullscreen-chatbot.dark-mode .conversation-item:hover {
  background-color: #333;
}

.fullscreen-chatbot.dark-mode .conversation-item.active {
  background-color: #2d3748;
}

.fullscreen-chatbot.dark-mode .message.user .message-bubble {
  background-color: #3b4a63;
  color: #e0e0e0;
}

.fullscreen-chatbot.dark-mode .message.bot .message-bubble {
  background-color: #2d3748;
  color: #e0e0e0;
}

.fullscreen-chatbot.dark-mode .input-container input {
  background-color: #333;
  color: #e0e0e0;
  border-color: #444;
}

.fullscreen-chatbot.dark-mode .welcome-message {
  background-color: #252525;
}

.fullscreen-chatbot.dark-mode .suggestion-buttons button {
  background-color: #333;
  color: #e0e0e0;
  border-color: #444;
}

.fullscreen-chatbot.dark-mode .suggestion-buttons button:hover {
  background-color: #444;
}

.fullscreen-chatbot.dark-mode .saved-message-item {
  background-color: #333;
  border-color: #444;
}

.fullscreen-chatbot.dark-mode .dropdown-menu {
  background-color: #333;
  border-color: #444;
}

.fullscreen-chatbot.dark-mode .dropdown-menu button:hover {
  background-color: #444;
}

/* Sidebar */
.chatbot-sidebar {
  width: 320px;
  height: 100%;
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width 0.3s ease;
  /* Smooth transition for mobile collapse */
}

.sidebar-section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #334155;
  margin: 16px 16px 8px 16px;
}

.sidebar-section-description {
  font-size: 0.75rem;
  color: #64748b;
  margin: 0 16px 16px 16px;
  line-height: 1.5;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sidebar-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.new-chat-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #00bcd4;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.new-chat-button:hover {
  background-color: #0097a7;
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.conversation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.conversation-item:hover {
  background-color: #f1f5f9;
}

.conversation-item.active {
  background-color: #e0f7fa;
}

.conversation-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #00bcd4;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.conversation-details {
  flex: 1;
  min-width: 0;
}

.conversation-title {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.conversation-preview {
  font-size: 0.75rem;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.conversation-date {
  font-size: 0.75rem;
  color: #94a3b8;
}

.sidebar-section {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
}

.sidebar-section h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.saved-messages-list {
  max-height: 200px;
  overflow-y: auto;
}

.saved-message-item {
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  position: relative;
}

.saved-message-text {
  font-size: 0.875rem;
  margin-bottom: 8px;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.saved-message-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.saved-message-actions button {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.saved-message-actions button:hover {
  color: #0284c7;
  background-color: rgba(2, 132, 199, 0.1);
}

.empty-state {
  text-align: center;
  padding: 16px;
  color: #64748b;
}

.empty-state-hint {
  font-size: 0.75rem;
  margin-top: 8px;
  font-style: italic;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  font-size: 1.5rem;
  color: #0284c7;
  margin-bottom: 1rem;
}

.loading-text {
  color: #64748b;
  font-size: 0.875rem;
}

.sidebar-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  font-size: 1.5rem;
  color: #0284c7;
}

.dark-mode .sidebar-loading-overlay {
  background-color: rgba(0, 0, 0, 0.5);
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
}

.theme-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  width: 100%;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background-color: #f1f5f9;
}

/* Main chat area */
.chatbot-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chatbot-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  z-index: 10;
}

.sidebar-toggle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: none;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 16px;
  color: #64748b;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover {
  background-color: #f1f5f9;
  color: #0284c7;
}

.header-title {
  flex: 1;
}

.header-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.header-subtitle {
  font-size: 0.875rem;
  color: #64748b;
}

.header-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.voice-controls {
  display: flex;
  gap: 8px;
}

.voice-toggle,
.minimize-button,
.read-aloud-toggle {
  height: 36px;
  border-radius: 18px;
  background: none;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
  padding: 0 12px;
  gap: 6px;
}

.control-label {
  font-size: 0.75rem;
  font-weight: 500;
}

.read-aloud-toggle.speaking {
  color: #0284c7;
  background-color: rgba(2, 132, 199, 0.1);
  border-color: rgba(2, 132, 199, 0.3);
}

.voice-toggle:hover,
.minimize-button:hover,
.read-aloud-toggle:hover {
  background-color: #f1f5f9;
  color: #0284c7;
}

/* Chat history */
.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #ffffff;
}

.welcome-message {
  max-width: 600px;
  margin: 40px auto;
  text-align: center;
  padding: 24px;
  background-color: #f8fafc;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.welcome-message h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.welcome-message p {
  margin: 0 0 16px 0;
  color: #64748b;
  line-height: 1.6;
}

.welcome-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 24px 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  text-align: left;
}

.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e0f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.feature-text {
  font-size: 0.875rem;
  color: #334155;
}

.welcome-suggestions {
  margin-top: 24px;
}

.welcome-suggestions h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.suggestion-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.suggestion-buttons button {
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 8px 16px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-buttons button:hover {
  background-color: #e0f7fa;
  border-color: #b2ebf2;
}

/* Message styling */
.message {
  display: flex;
  margin-bottom: 24px;
  animation: fadeIn 0.3s ease;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.user-avatar {
  color: #0284c7;
}

.bot-avatar {
  color: #00bcd4;
}

.message-content {
  flex: 1;
  max-width: calc(100% - 52px);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.message-sender {
  font-weight: 600;
  font-size: 0.875rem;
}

.message-time {
  font-size: 0.75rem;
  color: #64748b;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 0.9375rem;
  line-height: 1.6;
  max-width: 100%;
  word-wrap: break-word;
}

.message.user .message-bubble {
  background-color: #e0f7fa;
  color: #0c4a6e;
  border-bottom-right-radius: 4px;
}

.message.bot .message-bubble {
  background-color: #f1f5f9;
  color: #334155;
  border-bottom-left-radius: 4px;
}

/* Markdown styling remains unchanged unless specified */

/* Message actions */
.message-actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
  justify-content: flex-end;
}

.action-button {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  color: #0284c7;
  background-color: rgba(2, 132, 199, 0.1);
}

.action-button.active {
  color: #0284c7;
}

.action-dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 8px;
  min-width: 160px;
  z-index: 100;
  margin-bottom: 8px;
}

.dropdown-menu button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dropdown-menu button:hover {
  background-color: #f1f5f9;
}

/* Typing indicator remains unchanged */

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 24px;
  min-width: 60px;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: #64748b;
  border-radius: 50%;
  display: inline-block;
  margin: 0 2px;
  opacity: 0.6;
}

.typing-indicator span:nth-child(1) {
  animation: typing 1.5s infinite 0s;
}

.typing-indicator span:nth-child(2) {
  animation: typing 1.5s infinite 0.3s;
}

.typing-indicator span:nth-child(3) {
  animation: typing 1.5s infinite 0.6s;
}

@keyframes typing {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-5px);
  }
}

/* Chat input */
.chat-input {
  padding: 16px;
  background-color: #ffffff;
  border-top: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.input-container input {
  width: 100%;
  padding: 12px 48px 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 24px;
  font-size: 0.9375rem;
  outline: none;
  transition: all 0.2s ease;
}

.input-container input:focus {
  border-color: #00bcd4;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

.input-container input.listening {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  animation: pulse 1.5s infinite;
}

.mic-button {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.mic-button:hover {
  color: #0284c7;
}

.mic-button.listening {
  color: #ef4444;
  animation: pulse 1.5s infinite;
}

.send-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #00bcd4;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover {
  background-color: #0097a7;
}

.send-button:disabled {
  background-color: #cbd5e1;
  cursor: not-allowed;
}

.send-button.loading {
  background-color: #cbd5e1;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {

  /* Fullscreen container */
  .fullscreen-chatbot {
    flex-direction: column;
    /* Stack sidebar and main area vertically */
  }

  /* Sidebar */
  .chatbot-sidebar {
    position: fixed;
    /* Fixed position for overlay effect */
    top: 0;
    left: 0;
    bottom: 0;
    width: 280px;
    z-index: 20;
    transform: translateX(-100%);
    /* Hidden by default */
  }

  /* Show sidebar when toggled (controlled by showSidebar state in React) */
  .fullscreen-chatbot.show-sidebar .chatbot-sidebar {
    transform: translateX(0);
    /* Slide in when toggled */
  }

  .sidebar-header {
    padding: 12px;
  }

  .sidebar-header h2 {
    font-size: 1.125rem;
  }

  .new-chat-button {
    padding: 8px 12px;
    font-size: 0.875rem;
  }

  .conversation-item {
    padding: 10px;
    gap: 8px;
  }

  .conversation-icon {
    width: 28px;
    height: 28px;
  }

  .conversation-title {
    font-size: 0.875rem;
  }

  .conversation-preview,
  .conversation-date {
    font-size: 0.6875rem;
    max-width: 180px;
  }

  .sidebar-section {
    padding: 12px;
  }

  .sidebar-section h3 {
    font-size: 0.875rem;
  }

  .saved-message-item {
    padding: 10px;
  }

  .saved-message-text {
    font-size: 0.8125rem;
    max-height: 48px;
    -webkit-line-clamp: 2;
  }

  .sidebar-footer {
    padding: 12px;
  }

  .theme-toggle {
    padding: 6px;
    font-size: 0.875rem;
  }

  /* Main chat area */
  .chatbot-main {
    width: 100%;
    height: 100%;
  }

  .chatbot-header {
    padding: 12px;
    flex-wrap: wrap;
    gap: 8px;
  }

  .sidebar-toggle {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .header-title h2 {
    font-size: 1.125rem;
  }

  .header-subtitle {
    font-size: 0.75rem;
  }

  .header-controls {
    gap: 8px;
  }

  .voice-controls {
    gap: 4px;
  }

  .voice-toggle,
  .minimize-button,
  .read-aloud-toggle {
    height: 32px;
    padding: 0 8px;
    font-size: 0.75rem;
  }

  .control-label {
    display: none;
    /* Hide labels on mobile to save space */
  }

  .chat-history {
    padding: 12px;
  }

  .welcome-message {
    margin: 20px auto;
    padding: 16px;
    max-width: 100%;
  }

  .welcome-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }

  .welcome-message h3 {
    font-size: 1.25rem;
    margin-bottom: 12px;
  }

  .welcome-message p {
    font-size: 0.875rem;
    margin-bottom: 12px;
  }

  .welcome-features {
    grid-template-columns: 1fr;
    gap: 12px;
    margin: 16px 0;
  }

  .feature-icon {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .feature-text {
    font-size: 0.8125rem;
  }

  .welcome-suggestions {
    margin-top: 16px;
  }

  .welcome-suggestions h4 {
    font-size: 0.875rem;
    margin-bottom: 8px;
  }

  .suggestion-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .suggestion-buttons button {
    width: 100%;
    padding: 8px 12px;
    font-size: 0.8125rem;
  }

  /* Message styling */
  .message {
    margin-bottom: 16px;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    margin-right: 8px;
    font-size: 1.25rem;
  }

  .message-content {
    max-width: calc(100% - 40px);
  }

  .message-sender {
    font-size: 0.8125rem;
  }

  .message-time {
    font-size: 0.6875rem;
  }

  .message-bubble {
    padding: 10px 12px;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .message-actions {
    gap: 6px;
    margin-top: 2px;
  }

  .action-button {
    padding: 3px;
  }

  .dropdown-menu {
    min-width: 140px;
    padding: 6px;
    right: -10px;
    /* Adjust positioning for smaller screens */
  }

  .dropdown-menu button {
    padding: 6px;
    font-size: 0.8125rem;
  }

  /* Chat input */
  .chat-input {
    padding: 12px;
    gap: 8px;
  }

  .input-container input {
    padding: 10px 40px 10px 12px;
    font-size: 0.875rem;
    border-radius: 20px;
  }

  .mic-button {
    right: 8px;
    padding: 3px;
  }

  .send-button {
    width: 40px;
    height: 40px;
  }
}

/* Extra small screens (below 480px) */
@media (max-width: 480px) {
  .chatbot-sidebar {
    width: 100%;
    /* Full width on very small screens */
    max-width: 280px;
  }

  .conversation-preview {
    max-width: 140px;
  }

  .chatbot-header {
    padding: 10px;
  }

  .header-title h2 {
    font-size: 1rem;
  }

  .header-subtitle {
    font-size: 0.6875rem;
  }

  .voice-toggle,
  .minimize-button,
  .read-aloud-toggle {
    height: 28px;
    padding: 0 6px;
  }

  .chat-history {
    padding: 10px;
  }

  .welcome-message {
    margin: 16px auto;
    padding: 12px;
  }

  .welcome-icon {
    font-size: 32px;
  }

  .welcome-message h3 {
    font-size: 1.125rem;
  }

  .welcome-message p {
    font-size: 0.8125rem;
  }

  .message {
    margin-bottom: 12px;
  }

  .message-avatar {
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }

  .message-content {
    max-width: calc(100% - 36px);
  }

  .message-bubble {
    padding: 8px 10px;
    font-size: 0.8125rem;
  }

  .chat-input {
    padding: 10px;
  }

  .input-container input {
    padding: 8px 36px 8px 10px;
    font-size: 0.8125rem;
  }

  .send-button {
    width: 36px;
    height: 36px;
  }
}