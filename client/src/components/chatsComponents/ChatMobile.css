/* Mobile-specific styles for chat components */

/* Fix for mobile chat layout */
@media (max-width: 768px) {

  /* Fix for main content area */
  .chat-main-content {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    width: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
  }

  /* Ensure chat container takes full height */
  .chat-container {
    height: 100% !important;
    max-height: 100% !important;
  }

  /* Fix for chat messages container */
  .chat-messages-container {
    padding-bottom: 80px !important;
    min-height: calc(100vh - 200px) !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Fix for chat input position */
  .chat-input-container {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 30 !important;
    background-color: white !important;
    border-top: 1px solid #e5e7eb !important;
    width: 100% !important;
    padding: 8px !important;
  }

  /* Fix for chat header */
  .chat-header-container {
    position: sticky !important;
    top: 0 !important;
    z-index: 20 !important;
    background-color: white !important;
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
  }

  /* Fix for sidebar */
  .chat-sidebar {
    height: 100vh;
    top: 0 !important;
  }

  /* Fix for DefaultChat component */
  .default-chat {
    min-height: calc(100vh - 65px) !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
  }

  /* Show sidebar button */
  .show-sidebar-btn {
    position: fixed !important;
    top: 80px !important;
    left: 20px !important;
    z-index: 15 !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }

  /* Fix for search input on mobile */
  .chat-search-input:focus {
    position: relative !important;
    z-index: 40 !important;
  }

  /* Ensure sidebar stays visible when search is focused */
  /* Using a class that will be added via JS for better browser compatibility */
  .sidebar-search-focused {
    transform: translateX(0) !important;
  }
}