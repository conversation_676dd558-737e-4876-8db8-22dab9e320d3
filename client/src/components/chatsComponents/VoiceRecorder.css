.voice-recorder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  background-color: #f9fafb;
}

.recording-controls,
.playback-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.playback-controls {
  flex-direction: column;
}

.recording-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  font-size: 0.875rem;
  font-weight: 500;
}

.recording-dot {
  width: 10px;
  height: 10px;
  background-color: #ef4444;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.95);
    opacity: 1;
  }
}

.recording-time {
  font-family: monospace;
}

.record-button,
.stop-button,
.cancel-button,
.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.record-button {
  background-color: #ef4444;
  color: white;
}

.record-button:hover {
  background-color: #dc2626;
  transform: scale(1.05);
}

.stop-button {
  background-color: #ef4444;
  color: white;
}

.stop-button:hover {
  background-color: #dc2626;
}

.cancel-button {
  background-color: #f3f4f6;
  color: #6b7280;
}

.cancel-button:hover {
  background-color: #e5e7eb;
  color: #4b5563;
}

.send-button {
  background-color: #3b82f6;
  color: white;
}

.send-button:hover {
  background-color: #2563eb;
}

.send-button:disabled,
.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.audio-preview {
  width: 100%;
  height: 40px;
  border-radius: 20px;
  margin-bottom: 8px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

/* Dark mode styles */
.dark .voice-recorder {
  background-color: #1f2937;
}

.dark .cancel-button {
  background-color: #374151;
  color: #9ca3af;
}

.dark .cancel-button:hover {
  background-color: #4b5563;
  color: #d1d5db;
}

.dark .audio-preview {
  background-color: #374151;
  color: #e5e7eb;
}

/* Mobile styles */
@media (max-width: 640px) {
  .record-button,
  .stop-button,
  .cancel-button,
  .send-button {
    width: 36px;
    height: 36px;
  }
  
  .recording-time {
    font-size: 0.75rem;
  }
}
