/* Enhanced Notification Dropdown Styles */

.notification-dropdown {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    /* max-height: 80vh; */
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Header styles */
.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8fafc;
}

.notification-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-title>div {
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-icon {
    margin-right: 8px;
}

.close-button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
}

.notification-title h3 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    color: #1e293b;
}

.notification-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    padding: 0 6px;
    background-color: #ef4444;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 10px;
}

.notification-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background-color: #f1f5f9;
    color: #3b82f6;
}

.action-button.close-button:hover {
    background-color: #fee2e2;
    color: #ef4444;
}

/* Filter styles */
.notification-filters {
    display: flex;
    padding: 8px 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #ffffff;
}

.filter-button {
    padding: 6px 12px;
    background: none;
    border: none;
    border-radius: 16px;
    font-size: 0.875rem;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-button:hover {
    color: #3b82f6;
}

.filter-button.active {
    background-color: #eff6ff;
    color: #3b82f6;
    font-weight: 500;
}

/* Notification list styles */
.notification-list {
    flex: 1;
    overflow-y: auto;
    max-height: 400px;
    padding: 8px 0;
    transition: max-height 0.3s ease;
}

.notification-list.expanded {
    max-height: 600px;
}

/* Loading state */
.notification-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    color: #64748b;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #e2e8f0;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Empty state */
.notification-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    color: #64748b;
    text-align: center;
}

.empty-icon {
    font-size: 2rem;
    color: #94a3b8;
    margin-bottom: 12px;
}

.empty-action {
    margin-top: 12px;
    padding: 6px 12px;
    background-color: #f1f5f9;
    border: none;
    border-radius: 16px;
    font-size: 0.875rem;
    color: #3b82f6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.empty-action:hover {
    background-color: #e0f2fe;
}

/* Notification item styles */
.notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f8fafc;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.notification-item:hover {
    background-color: #f8fafc;
}

.notification-item.unread {
    background-color: #eff6ff;
}

.notification-item.unread:hover {
    background-color: #e0f2fe;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #3b82f6;
    border-radius: 0 2px 2px 0;
}

.notification-content {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: #64748b;
    flex-shrink: 0;
}

.icon-like {
    color: #ef4444;
}

.icon-comment {
    color: #3b82f6;
}

.icon-reply {
    color: #10b981;
}

.notification-text {
    font-size: 0.875rem;
    color: #334155;
    line-height: 1.5;
    flex: 1;
}

.notification-user {
    font-weight: 600;
    color: #0f172a;
    text-decoration: none;
}

.notification-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.notification-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 8px;
}

.notification-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.notification-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-time {
    font-size: 0.75rem;
    color: #94a3b8;
}

.notification-actions {
    display: none;
    align-items: center;
    gap: 4px;
}

.notification-item:hover .notification-actions {
    display: flex;
}

/* Footer styles */
.notification-footer {
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    text-align: center;
}

.view-all-button {
    padding: 8px 16px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    font-size: 0.875rem;
    color: #3b82f6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-all-button:hover {
    background-color: #eff6ff;
    border-color: #bfdbfe;
}

/* Notification detail view */
.notification-detail {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ffffff;
    z-index: 10;
    display: flex;
    flex-direction: column;
}

.detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8fafc;
}

.detail-header h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    color: #1e293b;
}

.back-button {
    padding: 6px 12px;
    background: none;
    border: none;
    color: #3b82f6;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-button:hover {
    text-decoration: underline;
}

.detail-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.detail-info {
    margin-top: 16px;
    padding: 16px;
    background-color: #f8fafc;
    border-radius: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.detail-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

.detail-value {
    font-size: 0.875rem;
    color: #334155;
}

.detail-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.detail-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    color: #3b82f6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.detail-button:hover {
    background-color: #eff6ff;
    border-color: #bfdbfe;
}

.detail-button.delete-button {
    color: #ef4444;
}

.detail-button.delete-button:hover {
    background-color: #fee2e2;
    border-color: #fecaca;
}

/* Responsive Styles for Mobile (Full-Screen) */


@media (max-width: 768px) {
    .notification-dropdown {
        /* Full-screen overlay */
        width: 100vw;
        height: 100vh;
        /* Remove max-height restriction */
        border-radius: 0;
        /* No rounded corners on full-screen */
        box-shadow: none;
        /* Remove shadow for seamless edge */
    }

    .notification-header {
        padding: 12px 16px;
        /* Slightly smaller padding */
    }

    .notification-title h3 {
        font-size: 0.95rem;
        /* Slightly smaller title */
    }

    .notification-badge {
        min-width: 18px;
        height: 18px;
        font-size: 0.7rem;
    }

    .action-button {
        width: 28px;
        height: 28px;
    }

    .notification-filters {
        padding: 6px 12px;
        flex-wrap: wrap;
        /* Allow filters to wrap if needed */
        gap: 4px;
    }

    .filter-button {
        padding: 5px 10px;
        font-size: 0.85rem;
    }

    .notification-list {
        max-height: none;
        /* Let it use available space */
        flex: 1;
        /* Expand to fill remaining height */
    }

    .notification-item {
        padding: 10px 12px;
    }

    .notification-icon {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .notification-text {
        font-size: 0.85rem;
    }

    .notification-time {
        font-size: 0.7rem;
    }

    .notification-footer {
        padding: 10px 12px;
    }

    .view-all-button {
        padding: 6px 12px;
        font-size: 0.85rem;
    }

    .notification-detail {
        position: fixed;
        /* Ensure detail view is also full-screen */
        width: 100vw;
        height: 100vh;
    }

    .detail-header {
        padding: 12px 16px;
    }

    .detail-header h4 {
        font-size: 0.95rem;
    }

    .back-button {
        padding: 5px 10px;
        font-size: 0.85rem;
    }

    .detail-content {
        padding: 12px;
    }

    .detail-info {
        padding: 12px;
    }

    .detail-item {
        flex-direction: column;
        /* Stack label and value on small screens */
        gap: 4px;
    }

    .detail-label,
    .detail-value {
        font-size: 0.85rem;
    }

    .detail-button {
        padding: 6px 12px;
        font-size: 0.85rem;
    }
}

/* Extra Small Screens (below 480px) */
@media (max-width: 480px) {
    .notification-header {
        padding: 10px 12px;
    }

    .notification-title h3 {
        font-size: 0.9rem;
    }

    .notification-badge {
        min-width: 16px;
        height: 16px;
        font-size: 0.65rem;
    }

    .action-button {
        width: 24px;
        height: 24px;
    }

    .notification-filters {
        padding: 4px 10px;
    }

    .filter-button {
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    .notification-item {
        padding: 8px 10px;
    }

    .notification-icon {
        width: 28px;
        height: 28px;
        font-size: 0.85rem;
    }

    .notification-text {
        font-size: 0.8rem;
    }

    .notification-time {
        font-size: 0.65rem;
    }

    .notification-footer {
        padding: 8px 10px;
    }

    .view-all-button {
        padding: 5px 10px;
        font-size: 0.8rem;
    }

    .detail-header {
        padding: 10px 12px;
    }

    .detail-header h4 {
        font-size: 0.9rem;
    }

    .back-button {
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    .detail-content {
        padding: 10px;
    }

    .detail-info {
        padding: 10px;
    }

    .detail-label,
    .detail-value {
        font-size: 0.8rem;
    }

    .detail-button {
        padding: 5px 10px;
        font-size: 0.8rem;
    }
}