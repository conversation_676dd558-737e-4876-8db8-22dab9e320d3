import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaFire, FaTimes, FaTrophy, FaMedal } from 'react-icons/fa';
import { createPortal } from 'react-dom';

const StreakPopup = ({ streakCount, longestStreak, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Small delay before showing the popup for better UX
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    // Delay the actual closing to allow for exit animation
    setTimeout(onClose, 300);
  };

  // Define milestone achievements
  const getMilestoneMessage = (streak) => {
    if (streak >= 365) return "One Year Strong! 🎉";
    if (streak >= 180) return "Half-Year Dedication! 🌟";
    if (streak >= 90) return "Three Month Milestone! 🔥";
    if (streak >= 60) return "Two Month Streak! 💪";
    if (streak >= 30) return "One Month Streak! 🏆";
    if (streak >= 14) return "Two Week Streak! 👏";
    if (streak >= 7) return "One Week Streak! 🌈";
    if (streak >= 3) return "Three Day Streak! 🌱";
    if (streak === 1) return "First Day! 🎯";
    return "Keep Going! 🚀";
  };

  // Get appropriate icon based on streak count
  const getStreakIcon = (streak) => {
    if (streak >= 30) return <FaTrophy className="text-yellow-500" />;
    if (streak >= 7) return <FaMedal className="text-blue-500" />;
    return <FaFire className="text-orange-500" />;
  };

  const popupContent = (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 flex items-center justify-center z-50 p-4 bg-black bg-opacity-50 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            className="bg-white w-full max-w-md rounded-xl shadow-2xl overflow-hidden"
            initial={{ scale: 0.9, y: 20 }}
            animate={{ scale: 1, y: 0 }}
            exit={{ scale: 0.9, y: 20 }}
            transition={{ type: "spring", bounce: 0.4 }}
          >
            {/* Close Button */}
            <button
              onClick={handleClose}
              className="absolute right-4 top-4 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Close popup"
            >
              <FaTimes className="w-5 h-5" />
            </button>

            {/* Header with gradient */}
            <div className="bg-gradient-to-r from-orange-400 to-pink-500 p-6 text-white text-center">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="flex justify-center mb-2"
              >
                <div className="w-16 h-16 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                  {getStreakIcon(streakCount)}
                </div>
              </motion.div>
              <motion.h2
                className="text-2xl font-bold"
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                Daily Streak!
              </motion.h2>
              <motion.p
                className="text-sm opacity-90"
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                You're on a roll! Keep up the momentum.
              </motion.p>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <div className="text-center flex-1">
                  <motion.div
                    className="text-4xl font-bold text-orange-500 flex items-center justify-center gap-2"
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.5, type: "spring" }}
                  >
                    <FaFire />
                    {streakCount}
                  </motion.div>
                  <p className="text-sm text-gray-600 mt-1">Current Streak</p>
                </div>

                {longestStreak > streakCount && (
                  <div className="text-center flex-1">
                    <motion.div
                      className="text-4xl font-bold text-blue-500 flex items-center justify-center gap-2"
                      initial={{ scale: 0.5, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: 0.6, type: "spring" }}
                    >
                      <FaTrophy />
                      {longestStreak}
                    </motion.div>
                    <p className="text-sm text-gray-600 mt-1">Longest Streak</p>
                  </div>
                )}
              </div>

              <motion.div
                className="text-center mb-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
              >
                <p className="text-lg font-medium text-gray-800">
                  {getMilestoneMessage(streakCount)}
                </p>
                <p className="text-sm text-gray-600 mt-2">
                  Come back tomorrow to keep your streak going!
                </p>
              </motion.div>

              <motion.button
                onClick={handleClose}
                className="w-full py-3 rounded-lg bg-gradient-to-r from-orange-400 to-pink-500 text-white font-medium hover:opacity-90 transition-opacity"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8 }}
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
              >
                Continue to SukoonSphere
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return createPortal(popupContent, document.body);
};

export default StreakPopup;
