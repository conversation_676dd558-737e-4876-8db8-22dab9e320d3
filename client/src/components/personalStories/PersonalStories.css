/* Jodit editor styles */
.jodit-container:not(.jodit_inline) {
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.jodit-toolbar__box {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.jodit-container:not(.jodit_inline) .jodit-wysiwyg {
  min-height: 200px;
  padding: 1rem;
}

/* Story content styles */
.article-body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
}

.story-content h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.story-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.story-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

.story-content p {
  margin-bottom: 1rem;
}

.story-content ul, .story-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.story-content li {
  margin-bottom: 0.5rem;
}

.story-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: #4a5568;
}

/* Button styles */
.btn-outline-purple {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid #9f7aea;
  border-radius: 0.375rem;
  background-color: transparent;
  color: #805ad5;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-outline-purple:hover {
  background-color: #9f7aea;
  color: white;
}
