import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { Fa<PERSON><PERSON>, Fa<PERSON>ause, Fa<PERSON>or<PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON>heck } from 'react-icons/fa';
import QuizTimer from './QuizTimer';
import QuizProgress from './QuizProgress';
import { useUser } from '../../context/UserContext';

const EnhancedQuizTaker = ({ quiz, onComplete }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [startTime, setStartTime] = useState(null);
  const [timeSpent, setTimeSpent] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [questionStartTime, setQuestionStartTime] = useState(null);
  const [questionTimes, setQuestionTimes] = useState({});
  const [flaggedQuestions, setFlaggedQuestions] = useState(new Set());
  const [showConfirmation, setShowConfirmation] = useState(false);

  const { user } = useUser();
  const currentQuestion = quiz.questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === quiz.questions.length - 1;
  const answeredQuestions = Object.keys(answers).map(q => parseInt(q) + 1);

  // Timer for tracking time spent
  useEffect(() => {
    if (!isActive) return;

    const timer = setInterval(() => {
      setTimeSpent(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [isActive]);

  // Track time per question
  useEffect(() => {
    if (isActive && questionStartTime) {
      const timer = setInterval(() => {
        const currentTime = Date.now();
        const timeOnQuestion = Math.floor((currentTime - questionStartTime) / 1000);
        setQuestionTimes(prev => ({
          ...prev,
          [currentQuestionIndex]: timeOnQuestion
        }));
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isActive, questionStartTime, currentQuestionIndex]);

  const startQuiz = () => {
    setIsActive(true);
    setStartTime(Date.now());
    setQuestionStartTime(Date.now());
    toast.success('Quiz started! Good luck!');
  };

  const pauseQuiz = () => {
    setIsActive(false);
    toast.info('Quiz paused');
  };

  const resumeQuiz = () => {
    setIsActive(true);
    setQuestionStartTime(Date.now());
    toast.success('Quiz resumed');
  };

  const handleAnswerSelect = (optionValue) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestionIndex]: optionValue
    }));
  };

  const goToNextQuestion = () => {
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      setQuestionStartTime(Date.now());
    }
  };

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      setQuestionStartTime(Date.now());
    }
  };

  const toggleFlag = () => {
    setFlaggedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(currentQuestionIndex)) {
        newSet.delete(currentQuestionIndex);
        toast.info('Question unflagged');
      } else {
        newSet.add(currentQuestionIndex);
        toast.info('Question flagged for review');
      }
      return newSet;
    });
  };

  const handleTimeUp = useCallback(() => {
    toast.error('Time\'s up! Submitting your answers...');
    submitQuiz();
  }, []);

  const submitQuiz = async () => {
    if (!user) {
      toast.error('Please log in to submit the quiz');
      return;
    }

    setIsActive(false);

    try {
      const responses = quiz.questions.map((question, index) => ({
        questionId: question.questionId,
        selectedOption: answers[index] || '',
        timeSpent: questionTimes[index] || 0,
      })).filter(response => response.selectedOption); // Only include answered questions

      const response = await fetch(`/api/v1/quizzes/${quiz._id}/attempt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          responses,
          timeSpent,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.msg);
        onComplete(result.attempt);
      } else {
        const error = await response.json();
        toast.error(error.msg || 'Failed to submit quiz');
      }
    } catch (error) {
      console.error('Error submitting quiz:', error);
      toast.error('Error submitting quiz');
    }
  };

  const handleSubmit = () => {
    const unansweredCount = quiz.questions.length - Object.keys(answers).length;
    
    if (unansweredCount > 0) {
      setShowConfirmation(true);
    } else {
      submitQuiz();
    }
  };

  const confirmSubmit = () => {
    setShowConfirmation(false);
    submitQuiz();
  };

  if (!isActive && !startTime) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">{quiz.title}</h2>
          <p className="text-gray-600 mb-6">{quiz.description}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">{quiz.questions.length}</div>
              <div className="text-sm text-blue-800">Questions</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">{quiz.estimatedTime}</div>
              <div className="text-sm text-green-800">Minutes</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600">{quiz.passingScore}%</div>
              <div className="text-sm text-purple-800">Passing Score</div>
            </div>
          </div>

          {quiz.timeLimit && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <p className="text-yellow-800">
                ⏰ This quiz has a time limit of {quiz.timeLimit} minutes
              </p>
            </div>
          )}

          <button
            onClick={startQuiz}
            className="inline-flex items-center px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-lg font-medium"
          >
            <FaPlay className="mr-2" />
            Start Quiz
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Quiz Area */}
        <div className="lg:col-span-3">
          {/* Timer */}
          {quiz.timeLimit && (
            <QuizTimer
              timeLimit={quiz.timeLimit}
              onTimeUp={handleTimeUp}
              isActive={isActive}
            />
          )}

          {/* Question Card */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Question {currentQuestionIndex + 1} of {quiz.questions.length}
              </h3>
              <div className="flex space-x-2">
                <button
                  onClick={toggleFlag}
                  className={`p-2 rounded-lg ${
                    flaggedQuestions.has(currentQuestionIndex)
                      ? 'bg-red-100 text-red-600'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                  title="Flag for review"
                >
                  <FaFlag />
                </button>
                <button
                  onClick={isActive ? pauseQuiz : resumeQuiz}
                  className="p-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200"
                  title={isActive ? 'Pause' : 'Resume'}
                >
                  {isActive ? <FaPause /> : <FaPlay />}
                </button>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-lg text-gray-800 leading-relaxed">
                {currentQuestion.text}
              </p>
              {currentQuestion.points > 1 && (
                <p className="text-sm text-blue-600 mt-2">
                  Worth {currentQuestion.points} points
                </p>
              )}
            </div>

            {/* Options */}
            <div className="space-y-3">
              {currentQuestion.options.map((option, index) => (
                <label
                  key={index}
                  className={`
                    flex items-center p-4 border rounded-lg cursor-pointer transition-all
                    ${answers[currentQuestionIndex] === option.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }
                  `}
                >
                  <input
                    type="radio"
                    name={`question-${currentQuestionIndex}`}
                    value={option.value}
                    checked={answers[currentQuestionIndex] === option.value}
                    onChange={() => handleAnswerSelect(option.value)}
                    className="mr-3 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-gray-800">{option.label}</span>
                </label>
              ))}
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center mt-8">
              <button
                onClick={goToPreviousQuestion}
                disabled={currentQuestionIndex === 0}
                className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FaBackward className="mr-2" />
                Previous
              </button>

              <div className="flex space-x-3">
                {!isLastQuestion ? (
                  <button
                    onClick={goToNextQuestion}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Next
                    <FaForward className="ml-2" />
                  </button>
                ) : (
                  <button
                    onClick={handleSubmit}
                    className="inline-flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <FaCheck className="mr-2" />
                    Submit Quiz
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <QuizProgress
            currentQuestion={currentQuestionIndex + 1}
            totalQuestions={quiz.questions.length}
            answeredQuestions={answeredQuestions}
            timeSpent={timeSpent}
          />

          {/* Flagged Questions */}
          {flaggedQuestions.size > 0 && (
            <div className="bg-white rounded-lg shadow-md p-4 mt-6">
              <h4 className="font-semibold text-gray-900 mb-3">Flagged Questions</h4>
              <div className="space-y-2">
                {Array.from(flaggedQuestions).map(questionIndex => (
                  <button
                    key={questionIndex}
                    onClick={() => setCurrentQuestionIndex(questionIndex)}
                    className="w-full text-left p-2 bg-red-50 text-red-800 rounded border border-red-200 hover:bg-red-100"
                  >
                    Question {questionIndex + 1}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Submit Quiz?
            </h3>
            <p className="text-gray-600 mb-6">
              You have {quiz.questions.length - Object.keys(answers).length} unanswered questions. 
              Are you sure you want to submit?
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmation(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={confirmSubmit}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedQuizTaker;
