import React, { useState, useEffect } from 'react';
import { FaTrophy, FaMedal, FaAward, FaClock, FaUser } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';

const QuizLeaderboard = ({ quizId, limit = 10 }) => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchLeaderboard();
  }, [quizId, limit]);

  const fetchLeaderboard = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/v1/quizzes/${quizId}/leaderboard?limit=${limit}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setLeaderboard(data.leaderboard || []);
      } else {
        setError('Failed to fetch leaderboard');
      }
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      setError('Error fetching leaderboard');
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <FaTrophy className="text-yellow-500" />;
      case 2:
        return <FaMedal className="text-gray-400" />;
      case 3:
        return <FaAward className="text-amber-600" />;
      default:
        return <span className="text-gray-600 font-bold">#{rank}</span>;
    }
  };

  const getRankBadgeColor = (rank) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white';
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Leaderboard</h3>
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Leaderboard</h3>
        <div className="text-center text-gray-500">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (leaderboard.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Leaderboard</h3>
        <div className="text-center text-gray-500">
          <FaTrophy className="mx-auto h-12 w-12 text-gray-300 mb-4" />
          <p>No attempts yet. Be the first to take this quiz!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center mb-6">
        <FaTrophy className="text-yellow-500 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900">Leaderboard</h3>
      </div>

      <div className="space-y-3">
        {leaderboard.map((entry, index) => {
          const rank = index + 1;
          return (
            <div
              key={entry.userId}
              className={`
                flex items-center justify-between p-4 rounded-lg border transition-all duration-200
                ${rank <= 3 
                  ? 'border-yellow-200 bg-gradient-to-r from-yellow-50 to-amber-50' 
                  : 'border-gray-200 bg-gray-50 hover:bg-gray-100'
                }
              `}
            >
              <div className="flex items-center space-x-4">
                {/* Rank */}
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-full font-bold
                  ${getRankBadgeColor(rank)}
                `}>
                  {rank <= 3 ? getRankIcon(rank) : rank}
                </div>

                {/* User Info */}
                <div className="flex items-center space-x-3">
                  {entry.avatar ? (
                    <img
                      src={entry.avatar}
                      alt={entry.username}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                      <FaUser className="text-white text-sm" />
                    </div>
                  )}
                  <div>
                    <div className="font-medium text-gray-900">
                      {entry.username}
                    </div>
                    <div className="text-sm text-gray-500">
                      {entry.totalAttempts} attempt{entry.totalAttempts !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="text-right">
                <div className="font-bold text-lg text-gray-900">
                  {entry.bestPercentage}%
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <FaClock className="mr-1" />
                  {formatTime(entry.fastestTime)}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="text-center text-sm text-gray-500">
          <p>
            Showing top {Math.min(limit, leaderboard.length)} performers
          </p>
          <p className="mt-1">
            Rankings based on best score and fastest completion time
          </p>
        </div>
      </div>
    </div>
  );
};

export default QuizLeaderboard;
