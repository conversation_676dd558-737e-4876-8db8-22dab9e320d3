import React from 'react';
import { FaCheckCircle, FaCircle, FaClock } from 'react-icons/fa';

const QuizProgress = ({ 
  currentQuestion, 
  totalQuestions, 
  answeredQuestions = [],
  timeSpent = 0,
  showTimeSpent = true 
}) => {
  const progressPercentage = (currentQuestion / totalQuestions) * 100;
  const answeredCount = answeredQuestions.length;

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Quiz Progress</h3>
        {showTimeSpent && (
          <div className="flex items-center text-gray-600">
            <FaClock className="mr-1 text-sm" />
            <span className="text-sm">{formatTime(timeSpent)}</span>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Question {currentQuestion} of {totalQuestions}</span>
          <span>{Math.round(progressPercentage)}% Complete</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className="bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Question Status */}
      <div className="grid grid-cols-10 gap-2 mb-4">
        {Array.from({ length: totalQuestions }, (_, index) => {
          const questionNumber = index + 1;
          const isAnswered = answeredQuestions.includes(questionNumber);
          const isCurrent = questionNumber === currentQuestion;
          
          return (
            <div
              key={questionNumber}
              className={`
                flex items-center justify-center w-8 h-8 rounded-full text-xs font-medium
                ${isCurrent 
                  ? 'bg-blue-600 text-white ring-2 ring-blue-300' 
                  : isAnswered 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-600'
                }
              `}
              title={`Question ${questionNumber} ${isAnswered ? '(Answered)' : '(Not answered)'}`}
            >
              {isAnswered ? (
                <FaCheckCircle className="text-green-600" />
              ) : (
                <span>{questionNumber}</span>
              )}
            </div>
          );
        })}
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-3 gap-4 text-center">
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="text-2xl font-bold text-blue-600">{currentQuestion}</div>
          <div className="text-xs text-blue-800">Current</div>
        </div>
        <div className="bg-green-50 rounded-lg p-3">
          <div className="text-2xl font-bold text-green-600">{answeredCount}</div>
          <div className="text-xs text-green-800">Answered</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-2xl font-bold text-gray-600">{totalQuestions - answeredCount}</div>
          <div className="text-xs text-gray-800">Remaining</div>
        </div>
      </div>

      {/* Motivational Message */}
      {progressPercentage > 0 && (
        <div className="mt-4 text-center">
          {progressPercentage === 100 ? (
            <span className="text-green-600 font-medium">🎉 All questions completed!</span>
          ) : progressPercentage >= 75 ? (
            <span className="text-blue-600 font-medium">🚀 Almost there! Keep going!</span>
          ) : progressPercentage >= 50 ? (
            <span className="text-blue-600 font-medium">💪 You're halfway through!</span>
          ) : progressPercentage >= 25 ? (
            <span className="text-blue-600 font-medium">📈 Great progress so far!</span>
          ) : (
            <span className="text-blue-600 font-medium">🌟 You've got this!</span>
          )}
        </div>
      )}
    </div>
  );
};

export default QuizProgress;
