import React, { useState, useEffect } from 'react';
import QuizSubmissionDialog from '../quizPageComponents/QuizSubmissionDialog';
import "../../assets/styles/global.css"

const QuizQuestions = ({ quizQuestionsList, quizId }) => {
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [selectedAnswer, setSelectedAnswer] = useState(null);
    const [selectedOptionKey, setSelectedOptionKey] = useState(null); // To track which option (a, b, c) was selected
    const [disableSelection, setDisableSelection] = useState(false);
    const [answers, setAnswers] = useState([]);
    const [isQuizCompleted, setIsQuizCompleted] = useState(false);
    const [score, setScore] = useState(0);

    // Retrieve the current question from the list
    const currentQuestion = quizQuestionsList[currentQuestionIndex] || {};
    const { question = '', option1 = '', option2 = '', option3 = '', option4 = '' } = currentQuestion;

    // Determine if we should show a, b, c options (for quiz 5) or regular options
    const isStressQuiz = quizId === 5;

    // Get options based on quiz type
    const getOptions = () => {
        if (isStressQuiz) {
            return [
                { key: 'a', text: option1 },
                { key: 'b', text: option2 },
                { key: 'c', text: option3 }
            ].filter(option => option.text); // Filter out empty options
        } else {
            return [option1, option2, option3, option4].filter(Boolean).map((text, index) => ({
                key: `option${index + 1}`,
                text
            }));
        }
    };

    const handleOptionClick = (event, option) => {
        if (!disableSelection) {
            handleAnswer(event, option);
        }
    };

    const getOptionClasses = (optionKey) => {
        return selectedOptionKey === optionKey
            ? 'bg-green-300 border-green-400'
            : '';
    };

    // Calculate score for stress quiz
    const calculateScore = (answers) => {
        if (!isStressQuiz) return 0;

        return answers.reduce((total, answer) => {
            const optionKey = answer.optionKey;
            if (optionKey === 'a') return total + 1;
            if (optionKey === 'c') return total + 2;
            return total; // 'b' is worth 0 points
        }, 0);
    };

    // Get assessment based on score
    const getAssessment = (score) => {
        if (!isStressQuiz) return null;

        if (score >= 45) {
            return {
                title: "High Stress Resilience",
                description: "You demonstrate excellent stress management skills and resilience. You likely have developed effective coping mechanisms."
            };
        } else if (score >= 31) {
            return {
                title: "Moderate Stress Resilience",
                description: "You have developed some effective stress management strategies, but there may be room for improvement in certain areas."
            };
        } else {
            return {
                title: "Developing Stress Resilience",
                description: "You may find that stress significantly impacts your wellbeing and daily functioning. Consider developing new coping strategies and seeking support."
            };
        }
    };

    useEffect(() => {
        if (isQuizCompleted) {
            const finalScore = calculateScore(answers);
            setScore(finalScore);
            document.getElementById('my_modal_3').showModal();
        }
    }, [isQuizCompleted, answers]);

    const handleAnswer = (event, option) => {
        setSelectedAnswer(option.text);
        setSelectedOptionKey(option.key);
        setDisableSelection(true);

        setAnswers((prevAnswers) => [
            ...prevAnswers,
            {
                question,
                selectedOption: option.text,
                optionKey: option.key
            }
        ]);

        if (currentQuestionIndex < quizQuestionsList.length - 1) {
            setCurrentQuestionIndex(currentQuestionIndex + 1);
            setSelectedAnswer(null);
            setSelectedOptionKey(null);
            setDisableSelection(false);
        } else {
            setIsQuizCompleted(true);
            setCurrentQuestionIndex(0);
            setSelectedAnswer(null);
            setSelectedOptionKey(null);
        }
    };

    const resetToDefault = () => {
        setCurrentQuestionIndex(0);
        setSelectedAnswer(null);
        setSelectedOptionKey(null);
        setDisableSelection(false);
        setIsQuizCompleted(false);
        setAnswers([]);
        setScore(0);
    }

    useEffect(() => {
        resetToDefault()
    }, [quizQuestionsList]);

    const options = getOptions();

    return (
        <>
            <div className='space-y-2'>
                <div className='bg-[var(--primary)] rounded-t-[20px] p-4'>
                    <div className="flex justify-between items-center">
                        <h1 className="text-2xl font-bold text-[var(--white-color)]">Question {currentQuestionIndex + 1}</h1>
                        <div className="bg-[var(--btn-primary)] text-white px-4 py-2 rounded-full text-sm">
                            {currentQuestionIndex + 1}/{quizQuestionsList.length}
                        </div>
                    </div>
                    <hr className="border-gray-300 mt-4" />
                    <div>
                        <h3 className="text-base md:text-lg text-[--white-color] my-2">{question}</h3>
                    </div>
                </div>

                <ul className="space-y-2 rounded-xl p-2">
                    {options.map((option, index) => (
                        <li
                            key={index}
                            onClick={(event) => handleOptionClick(event, option)}
                            className={`p-2 bg-[var(--body)] rounded-lg text-black cursor-pointer hover:border-[var(--primary)] hover:border-l-4 hover:glossy-effect-bar transition-all duration-200 ease-in-out ${getOptionClasses(option.key)}`}
                        >
                            {isStressQuiz && (
                                <span className="inline-block w-6 h-6 mr-2 bg-[var(--primary)] text-white rounded-full text-center">
                                    {option.key}
                                </span>
                            )}
                            {option.text}
                        </li>
                    ))}
                </ul>
            </div>
            <QuizSubmissionDialog
                answers={answers}
                score={score}
                assessment={getAssessment(score)}
                isStressQuiz={isStressQuiz}
            />
        </>
    );
};

// Wrap with React.memo to prevent unnecessary re-renders
export default React.memo(QuizQuestions);
