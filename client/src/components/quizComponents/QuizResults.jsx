import React, { useState } from 'react';
import { FaTrophy, FaClock, FaCheckCircle, FaTimesCircle, FaRedo, FaShare, FaDownload } from 'react-icons/fa';
import QuizLeaderboard from './QuizLeaderboard';

const QuizResults = ({ attempt, quiz, onRetake }) => {
  const [showDetailedResults, setShowDetailedResults] = useState(false);
  const [activeTab, setActiveTab] = useState('summary');

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getGradeColor = (percentage) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-blue-600';
    if (percentage >= 70) return 'text-yellow-600';
    if (percentage >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const getGradeBadge = (percentage) => {
    if (percentage >= 90) return { label: 'Excellent', color: 'bg-green-100 text-green-800' };
    if (percentage >= 80) return { label: 'Good', color: 'bg-blue-100 text-blue-800' };
    if (percentage >= 70) return { label: 'Fair', color: 'bg-yellow-100 text-yellow-800' };
    if (percentage >= 60) return { label: 'Pass', color: 'bg-orange-100 text-orange-800' };
    return { label: 'Needs Improvement', color: 'bg-red-100 text-red-800' };
  };

  const shareResults = () => {
    const text = `I just completed "${quiz.title}" and scored ${attempt.percentage}%! 🎉`;
    if (navigator.share) {
      navigator.share({
        title: 'Quiz Results',
        text,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(text);
      toast.success('Results copied to clipboard!');
    }
  };

  const downloadCertificate = () => {
    // This would generate a PDF certificate
    toast.info('Certificate download feature coming soon!');
  };

  const correctAnswers = attempt.responses.filter(r => r.isCorrect).length;
  const totalQuestions = attempt.responses.length;
  const gradeBadge = getGradeBadge(attempt.percentage);

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="mb-4">
          {attempt.passed ? (
            <FaTrophy className="mx-auto h-16 w-16 text-yellow-500" />
          ) : (
            <div className="mx-auto h-16 w-16 bg-gray-200 rounded-full flex items-center justify-center">
              <FaTimesCircle className="h-8 w-8 text-gray-500" />
            </div>
          )}
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {attempt.passed ? 'Congratulations!' : 'Quiz Completed'}
        </h1>
        <p className="text-gray-600">
          You've completed "{quiz.title}"
        </p>
      </div>

      {/* Score Summary */}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className={`text-3xl font-bold ${getGradeColor(attempt.percentage)}`}>
              {attempt.percentage}%
            </div>
            <div className="text-sm text-gray-600">Final Score</div>
            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-2 ${gradeBadge.color}`}>
              {gradeBadge.label}
            </span>
          </div>
          
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-3xl font-bold text-green-600">
              {correctAnswers}/{totalQuestions}
            </div>
            <div className="text-sm text-gray-600">Correct Answers</div>
          </div>
          
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-3xl font-bold text-purple-600">
              {formatTime(attempt.timeSpent)}
            </div>
            <div className="text-sm text-gray-600">Time Spent</div>
          </div>
          
          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="text-3xl font-bold text-yellow-600">
              #{attempt.attemptNumber}
            </div>
            <div className="text-sm text-gray-600">Attempt</div>
          </div>
        </div>

        {/* Pass/Fail Status */}
        <div className="mt-6 text-center">
          <div className={`inline-flex items-center px-4 py-2 rounded-full ${
            attempt.passed 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {attempt.passed ? (
              <>
                <FaCheckCircle className="mr-2" />
                Passed! (Required: {quiz.passingScore}%)
              </>
            ) : (
              <>
                <FaTimesCircle className="mr-2" />
                Not Passed (Required: {quiz.passingScore}%)
              </>
            )}
          </div>
        </div>

        {/* Assessment Result */}
        {attempt.assessmentResult && (
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">
              {attempt.assessmentResult.title}
            </h3>
            <p className="text-blue-800">
              {attempt.assessmentResult.description}
            </p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap justify-center gap-4 mb-8">
        {quiz.allowRetakes && (
          <button
            onClick={onRetake}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <FaRedo className="mr-2" />
            Retake Quiz
          </button>
        )}
        
        <button
          onClick={shareResults}
          className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          <FaShare className="mr-2" />
          Share Results
        </button>
        
        {attempt.passed && (
          <button
            onClick={downloadCertificate}
            className="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            <FaDownload className="mr-2" />
            Certificate
          </button>
        )}
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('summary')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'summary'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Summary
            </button>
            <button
              onClick={() => setActiveTab('detailed')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'detailed'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Detailed Results
            </button>
            <button
              onClick={() => setActiveTab('leaderboard')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'leaderboard'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Leaderboard
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'summary' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Strengths</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {attempt.percentage >= 80 && <li>• Excellent overall performance</li>}
                      {attempt.timeSpent < quiz.estimatedTime * 60 && <li>• Completed efficiently</li>}
                      {correctAnswers > totalQuestions * 0.7 && <li>• Strong understanding of concepts</li>}
                    </ul>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Areas for Improvement</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {attempt.percentage < 70 && <li>• Review core concepts</li>}
                      {attempt.timeSpent > quiz.estimatedTime * 60 * 1.5 && <li>• Work on time management</li>}
                      {correctAnswers < totalQuestions * 0.6 && <li>• Additional practice recommended</li>}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'detailed' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Question-by-Question Review</h3>
              <div className="space-y-4">
                {attempt.responses.map((response, index) => {
                  const question = quiz.questions.find(q => q.questionId === response.questionId);
                  const selectedOption = question?.options.find(opt => opt.value === response.selectedOption);
                  const correctOption = question?.options.find(opt => opt.isCorrect);
                  
                  return (
                    <div key={response.questionId} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <h4 className="font-medium text-gray-900">
                          Question {index + 1}: {question?.text}
                        </h4>
                        <div className="flex items-center">
                          {response.isCorrect ? (
                            <FaCheckCircle className="text-green-500 mr-2" />
                          ) : (
                            <FaTimesCircle className="text-red-500 mr-2" />
                          )}
                          <span className="text-sm text-gray-600">
                            {response.pointsEarned} / {question?.points || 1} points
                          </span>
                        </div>
                      </div>
                      
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium">Your answer: </span>
                          <span className={response.isCorrect ? 'text-green-600' : 'text-red-600'}>
                            {selectedOption?.label || 'No answer'}
                          </span>
                        </div>
                        
                        {!response.isCorrect && correctOption && (
                          <div>
                            <span className="font-medium">Correct answer: </span>
                            <span className="text-green-600">{correctOption.label}</span>
                          </div>
                        )}
                        
                        {question?.explanation && (quiz.showCorrectAnswers || response.isCorrect) && (
                          <div className="bg-blue-50 border border-blue-200 rounded p-3 mt-2">
                            <span className="font-medium text-blue-900">Explanation: </span>
                            <span className="text-blue-800">{question.explanation}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'leaderboard' && (
            <QuizLeaderboard quizId={quiz._id} />
          )}
        </div>
      </div>
    </div>
  );
};

export default QuizResults;
