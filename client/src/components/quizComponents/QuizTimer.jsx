import React, { useState, useEffect } from 'react';
import { FaClock, FaExclamationTriangle } from 'react-icons/fa';

const QuizTimer = ({ 
  timeLimit, 
  onTimeUp, 
  isActive = true, 
  showWarning = true,
  warningThreshold = 60 // seconds
}) => {
  const [timeLeft, setTimeLeft] = useState(timeLimit * 60); // Convert minutes to seconds
  const [isWarning, setIsWarning] = useState(false);

  useEffect(() => {
    if (!isActive || timeLeft <= 0) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        const newTime = prev - 1;
        
        // Check for warning threshold
        if (showWarning && newTime <= warningThreshold && !isWarning) {
          setIsWarning(true);
        }
        
        // Time's up
        if (newTime <= 0) {
          onTimeUp();
          return 0;
        }
        
        return newTime;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isActive, timeLeft, onTimeUp, showWarning, warningThreshold, isWarning]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    const totalTime = timeLimit * 60;
    return ((totalTime - timeLeft) / totalTime) * 100;
  };

  const getTimerColor = () => {
    if (timeLeft <= 30) return 'text-red-600';
    if (isWarning) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressColor = () => {
    if (timeLeft <= 30) return 'bg-red-500';
    if (isWarning) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (!timeLimit) return null;

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <FaClock className={`mr-2 ${getTimerColor()}`} />
          <span className="text-sm font-medium text-gray-700">Time Remaining</span>
        </div>
        {isWarning && (
          <div className="flex items-center text-yellow-600">
            <FaExclamationTriangle className="mr-1" />
            <span className="text-xs font-medium">Hurry up!</span>
          </div>
        )}
      </div>
      
      <div className="flex items-center justify-between mb-2">
        <span className={`text-2xl font-bold ${getTimerColor()}`}>
          {formatTime(timeLeft)}
        </span>
        <span className="text-sm text-gray-500">
          of {formatTime(timeLimit * 60)}
        </span>
      </div>
      
      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-1000 ${getProgressColor()}`}
          style={{ width: `${getProgressPercentage()}%` }}
        />
      </div>
      
      {timeLeft <= 30 && (
        <div className="mt-2 text-center">
          <span className="text-xs text-red-600 font-medium animate-pulse">
            Time is running out!
          </span>
        </div>
      )}
    </div>
  );
};

export default QuizTimer;
