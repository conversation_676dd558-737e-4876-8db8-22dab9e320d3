/* Therapy Components Styles */

/* Session List */
.therapy-session-list {
  flex: 1;
  overflow-y: auto;
}

.therapy-no-sessions {
  padding: 2rem;
  text-align: center;
  color: #666;
}

.therapy-session-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.therapy-session-item:hover {
  background-color: #f8f9fa;
}

.therapy-session-item.active {
  background-color: #f0f4ff;
  border-left: 3px solid #4a6cf7;
}

.therapy-session-icon {
  margin-right: 1rem;
  color: #4a6cf7;
}

.therapy-session-completed-icon {
  color: #4caf50;
}

.therapy-session-active-icon {
  color: #4a6cf7;
}

.therapy-session-info {
  flex: 1;
  min-width: 0;
}

.therapy-session-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.therapy-session-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #666;
}

.therapy-status-badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-weight: 500;
}

.therapy-status-badge.active {
  background-color: #e3f2fd;
  color: #1976d2;
}

.therapy-status-badge.completed {
  background-color: #e8f5e9;
  color: #388e3c;
}

/* Chat Component */
.therapy-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8f9fa;
}

.therapy-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.therapy-message {
  display: flex;
  max-width: 80%;
}

.therapy-message.user {
  align-self: flex-end;
}

.therapy-message.therapist {
  align-self: flex-start;
}

.therapy-message-content {
  padding: 1rem;
  border-radius: 1rem;
  position: relative;
}

.therapy-message.user .therapy-message-content {
  background-color: #4a6cf7;
  color: white;
  border-bottom-right-radius: 0;
}

.therapy-message.therapist .therapy-message-content {
  background-color: white;
  color: #333;
  border-bottom-left-radius: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.therapy-message-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.therapy-message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
}

.therapy-message-time {
  font-size: 0.7rem;
  opacity: 0.7;
}

.therapy-message-speak-button {
  background: none;
  border: none;
  color: #4a6cf7;
  font-size: 0.8rem;
  cursor: pointer;
  padding: 0.2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.therapy-message-speak-button:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.therapy-message-speak-button:disabled {
  color: #c5c5c5;
  cursor: not-allowed;
}

.therapy-chat-footer {
  background-color: white;
  border-top: 1px solid #e0e0e0;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Therapy Chat Input */
.therapy-chat-input {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
  gap: 12px;
}

/* Input and Send Button Group */
.therapy-chat-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

/* Input Container */
.therapy-chat-input-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.therapy-input {
  width: 100%;
  padding: 12px 48px 12px 16px;
  /* Space for mic button */
  font-size: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  outline: none;
  background-color: #f9fafb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  resize: none;
  line-height: 1.5;
}

.therapy-input:focus {
  border-color: var(--sukoon-primary, #4a6cf7);
  background-color: #fff;
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.2);
}

.therapy-input.listening {
  border-color: var(--sukoon-accent, #ff6b6b);
  background-color: #fff1f1;
  animation: pulse 1.5s infinite;
  caret-color: transparent;
}

.therapy-input:disabled {
  background-color: #e5e7eb;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Mic Button */
.therapy-mic-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6b7280;
  font-size: 1.25rem;
  cursor: pointer;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.therapy-mic-button:hover:not(:disabled) {
  color: var(--sukoon-primary, #4a6cf7);
  background-color: rgba(74, 108, 247, 0.1);
}

.therapy-mic-button.listening {
  color: var(--sukoon-accent, #ff6b6b);
  background-color: rgba(255, 107, 107, 0.1);
  animation: pulse 1.5s infinite;
}

.therapy-mic-button:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

/* Chat Actions */
.therapy-chat-actions {
  display: flex;
  justify-content: flex-end;
  /* Align complete button to the right */
  gap: 10px;
}

/* TTS Button */
.therapy-tts-button {
  background-color: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  width: 44px;
  height: 44px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.therapy-tts-button:hover {
  background-color: #f0f4ff;
  color: #4a6cf7;
  border-color: #4a6cf7;
}

.therapy-tts-button.active {
  background-color: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

/* Send Button */
.therapy-send-button {
  background-color: var(--sukoon-primary, #4a6cf7);
  color: white;
  border: none;
  border-radius: 50%;
  padding: 0;
  width: 44px;
  height: 44px;
  cursor: pointer;
  font-size: 1.25rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  /* Prevent shrinking */
}

.therapy-send-button:hover:not(:disabled) {
  background-color: #3b5cdb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.therapy-send-button:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
  box-shadow: none;
}

/* Complete Button */
.therapy-complete-button {
  background-color: transparent;
  color: var(--sukoon-primary, #4a6cf7);
  border: 1px solid var(--sukoon-primary, #4a6cf7);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.therapy-complete-button:hover {
  background-color: var(--sukoon-light, #f0f4ff);
  color: #3b5cdb;
  border-color: #3b5cdb;
}

/* Animations */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .therapy-chat-input {
    padding: 12px;
    gap: 10px;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
  }

  .therapy-chat-input-group {
    gap: 8px;
  }

  .therapy-input {
    padding: 10px 40px 10px 12px;
    font-size: 0.95rem;
    border-radius: 10px;
  }

  .therapy-mic-button {
    width: 32px;
    height: 32px;
    font-size: 1.1rem;
    right: 6px;
  }

  .therapy-send-button {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .therapy-complete-button {
    padding: 6px 12px;
    font-size: 0.85rem;
    height: 40px;
    width: auto;
    /* Allow natural width */
  }
}

@media (max-width: 480px) {
  .therapy-chat-input {
    padding: 10px;
    gap: 8px;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
  }

  .therapy-input {
    padding: 8px 36px 8px 10px;
    font-size: 0.9rem;
  }

  .therapy-mic-button {
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }

  .therapy-send-button {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .therapy-complete-button {
    padding: 6px 10px;
    font-size: 0.8rem;
    height: 36px;
  }
}

.therapy-session-completed {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #f0f4ff;
  color: #4a6cf7;
  border-radius: 0.5rem;
  font-size: 0.9rem;
}

.therapy-assessment-button {
  background-color: #f0f4ff;
  color: #4a6cf7;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.therapy-assessment-button:hover {
  background-color: #e3eaff;
}

/* Empty State */
.therapy-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  padding: 2rem;
  color: #666;
}

.therapy-empty-icon {
  font-size: 3rem;
  color: #4a6cf7;
  opacity: 0.3;
  margin-bottom: 1rem;
}

.therapy-empty-state h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.therapy-empty-state p {
  max-width: 400px;
  line-height: 1.5;
}

/* Modal */
.therapy-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.therapy-modal {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.therapy-modal h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: #333;
}

.therapy-mood-rating {
  margin: 1.5rem 0;
}

.therapy-mood-scale {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.therapy-mood-numbers {
  display: flex;
  flex: 1;
  justify-content: space-between;
}

.therapy-mood-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.therapy-mood-number.active {
  background-color: #4a6cf7;
  color: white;
}

.therapy-feedback {
  margin-bottom: 1.5rem;
}

.therapy-feedback label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.therapy-feedback textarea {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  padding: 0.75rem;
  resize: none;
  min-height: 100px;
  font-family: inherit;
  font-size: 0.9rem;
}

.therapy-feedback textarea:focus {
  outline: none;
  border-color: #4a6cf7;
}

.therapy-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.therapy-modal-cancel {
  background-color: transparent;
  color: #666;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.therapy-modal-cancel:hover {
  background-color: #f0f0f0;
}

.therapy-modal-confirm {
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.therapy-modal-confirm:hover {
  background-color: #3a5bd9;
}

.therapy-modal-confirm:disabled {
  background-color: #c5c5c5;
  cursor: not-allowed;
}

/* Assessment Component */
.therapy-assessment {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
}

.therapy-assessment-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.therapy-assessment-header h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  color: #333;
}

.therapy-assessment-header p {
  margin-bottom: 1rem;
  color: #666;
  font-size: 0.9rem;
}

.therapy-assessment-progress {
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.therapy-assessment-progress-bar {
  height: 100%;
  background-color: #4a6cf7;
  transition: width 0.3s ease;
}

.therapy-assessment-category {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.therapy-assessment-category-label {
  color: #666;
}

.therapy-assessment-category-name {
  font-weight: 500;
  color: #4a6cf7;
}

.therapy-assessment-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.therapy-assessment-question h4 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  color: #333;
  line-height: 1.5;
}

.therapy-assessment-scale {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: space-between;
}

.therapy-scale-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
}

.therapy-scale-option:hover {
  border-color: #4a6cf7;
  background-color: #f0f4ff;
}

.therapy-scale-option.active {
  border-color: #4a6cf7;
  background-color: #4a6cf7;
  color: white;
}

.therapy-scale-value {
  font-weight: 600;
  font-size: 1.1rem;
}

.therapy-scale-label {
  font-size: 0.8rem;
  text-align: center;
}

.therapy-assessment-choices {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.therapy-choice-option {
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  font-size: 0.9rem;
}

.therapy-choice-option:hover {
  border-color: #4a6cf7;
  background-color: #f0f4ff;
}

.therapy-choice-option.active {
  border-color: #4a6cf7;
  background-color: #4a6cf7;
  color: white;
}

.therapy-assessment-open-ended textarea {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  padding: 0.75rem;
  resize: none;
  min-height: 120px;
  font-family: inherit;
  font-size: 0.9rem;
}

.therapy-assessment-open-ended textarea:focus {
  outline: none;
  border-color: #4a6cf7;
}

.therapy-assessment-footer {
  padding: 1.5rem;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.therapy-assessment-skip {
  background-color: transparent;
  color: #666;
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.therapy-assessment-skip:hover {
  color: #333;
  text-decoration: underline;
}

.therapy-assessment-navigation {
  display: flex;
  gap: 1rem;
}

.therapy-assessment-prev,
.therapy-assessment-next,
.therapy-assessment-submit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.therapy-assessment-prev {
  background-color: transparent;
  color: #666;
  border: 1px solid #e0e0e0;
}

.therapy-assessment-prev:hover {
  background-color: #f0f0f0;
}

.therapy-assessment-prev:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.therapy-assessment-next {
  background-color: #4a6cf7;
  color: white;
  border: none;
}

.therapy-assessment-next:hover {
  background-color: #3a5bd9;
}

.therapy-assessment-next:disabled {
  background-color: #c5c5c5;
  cursor: not-allowed;
}

.therapy-assessment-submit {
  background-color: #4caf50;
  color: white;
  border: none;
}

.therapy-assessment-submit:hover {
  background-color: #3d8b40;
}

.therapy-assessment-submit:disabled {
  background-color: #c5c5c5;
  cursor: not-allowed;
}

.therapy-assessment-loading,
.therapy-assessment-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
  color: #666;
}

.therapy-assessment-loading .spin {
  font-size: 2rem;
  color: #4a6cf7;
  margin-bottom: 1rem;
  animation: spin 1s linear infinite;
}

.therapy-assessment-error button {
  margin-top: 1rem;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
}

/* Action Plans Component */
.therapy-action-plans {
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.therapy-empty-action-plans {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
  color: #666;
}

.therapy-empty-icon {
  font-size: 3rem;
  color: #4a6cf7;
  opacity: 0.3;
  margin-bottom: 1rem;
}

.therapy-empty-action-plans h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.therapy-empty-action-plans p {
  max-width: 500px;
  line-height: 1.5;
}

.therapy-action-plan {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.therapy-action-plan-header {
  padding: 1.25rem;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
}

.therapy-action-plan-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.therapy-action-plan-date {
  font-size: 0.8rem;
  color: #666;
}

.therapy-action-plan-description {
  padding: 1.25rem;
  border-bottom: 1px solid #f0f0f0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #333;
}

.therapy-action-plan-tasks {
  padding: 1.25rem;
}

.therapy-action-plan-tasks h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: #333;
}

.therapy-no-tasks {
  color: #666;
  font-style: italic;
  font-size: 0.9rem;
}

.therapy-action-plan-tasks ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.therapy-task {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

.therapy-task:hover {
  background-color: #f0f0f0;
}

.therapy-task.completed {
  opacity: 0.7;
}

.therapy-task.completed .therapy-task-text {
  text-decoration: line-through;
}

.therapy-task-checkbox {
  background: none;
  border: none;
  padding: 0;
  color: #4a6cf7;
  cursor: pointer;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.therapy-task-checkbox:disabled {
  cursor: not-allowed;
}

.therapy-task-content {
  flex: 1;
}

.therapy-task-text {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.therapy-task-due-date {
  font-size: 0.8rem;
  color: #666;
}

/* Insights Component */
.therapy-insights {
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.therapy-empty-insights {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
  color: #666;
}

.therapy-summary {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.therapy-summary-header {
  padding: 1.25rem;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f8f9fa;
}

.therapy-summary-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.therapy-summary-content {
  padding: 1.5rem;
  font-size: 0.95rem;
  line-height: 1.6;
  color: #333;
  position: relative;
}

.therapy-quote-marks {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.therapy-quote-left {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  font-size: 1.5rem;
  color: #f0f0f0;
}

.therapy-quote-right {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  font-size: 1.5rem;
  color: #f0f0f0;
}

.therapy-insights-section,
.therapy-recommendations-section {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.therapy-insights-header,
.therapy-recommendations-header {
  padding: 1.25rem;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.therapy-insights-header h3,
.therapy-recommendations-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.therapy-insights-header svg,
.therapy-recommendations-header svg {
  color: #4a6cf7;
}

.therapy-insights-list,
.therapy-recommendations-list {
  list-style: none;
  padding: 1.25rem;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.therapy-insight-item {
  display: flex;
  gap: 1rem;
}

.therapy-insight-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #4a6cf7;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.therapy-insight-content {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #333;
}

.therapy-recommendation-item {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

.therapy-recommendation-bullet {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4a6cf7;
  margin-top: 0.5rem;
}

.therapy-recommendation-content {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #333;
}

/* Animation */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}