import React, { createContext, useContext, useReducer, useEffect, useState } from "react";

import customFetch from "@/utils/customFetch";
import axios from "axios";
import StreakPopup from "@/components/notifications/StreakPopup";

const UserContext = createContext();

const initialState = {
  user: null,
  isLoading: false,
  error: null,
  showStreakPopup: false,
  streakInfo: null
};

const userReducer = (state, action) => {
  switch (action.type) {
    case "SET_USER":
      return {
        ...state,
        user: action.payload,
        isLoading: false,
      };

    case "REMOVE_USER":
      return {
        ...state,
        user: null,
        isLoading: false,
      };

    case "SET_ERROR":
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };

    case "SET_LOADING":
      return {
        ...state,
        isLoading: action.payload,
      };

    case "SET_STREAK_POPUP":
      return {
        ...state,
        showStreakPopup: action.payload,
      };

    case "SET_STREAK_INFO":
      return {
        ...state,
        streakInfo: action.payload,
      };

    default:
      return state;
  }
};

export const UserProvider = ({ children }) => {
  const [state, dispatch] = useReducer(userReducer, initialState);

  // User actions

  const login = async (userData) => {
    try {
      dispatch({ type: "SET_LOADING", payload: true });

      const { data } = await axios.post("/api/v1/auth/login", userData);

      // Store user data in localStorage
      if (data.user) {
        localStorage.setItem("user", JSON.stringify(data.user));
        localStorage.setItem("isAuthenticated", "true");
      }

      dispatch({ type: "SET_USER", payload: data.user });

      // Check if we should show streak popup
      await checkAndShowStreakPopup(data.user);

      return { success: true };
    } catch (error) {
      dispatch({
        type: "SET_ERROR",
        payload: error.response?.data?.msg || "Login failed",
      });

      return { error: error.response?.data?.msg || "Login failed" };
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  const logout = async () => {
    try {
      await customFetch.delete("/auth/logout");

      // Clear user data from localStorage

      localStorage.removeItem("user");

      localStorage.removeItem("isAuthenticated");

      dispatch({ type: "REMOVE_USER" });
    } catch (error) {
      dispatch({ type: "SET_ERROR", payload: error.message });
    }
  };

  // Check if we should show streak popup
  const checkAndShowStreakPopup = async (user) => {
    try {
      // Check if we've already shown the streak popup today
      const lastStreakPopupDate = localStorage.getItem("lastStreakPopupDate");
      const today = new Date().toDateString();

      // If we've already shown the popup today, don't show it again
      if (lastStreakPopupDate === today) {
        return;
      }

      // Fetch the user's gamification data to get streak information
      const { data } = await customFetch.get('/user/gamification');

      if (data && data.streakProgress) {
        // Store streak info
        dispatch({ type: "SET_STREAK_INFO", payload: data.streakProgress });

        // Only show popup if user has a streak
        if (data.streakProgress.current > 0) {
          // Show streak popup
          dispatch({ type: "SET_STREAK_POPUP", payload: true });

          // Remember that we've shown the popup today
          localStorage.setItem("lastStreakPopupDate", today);
        }
      }
    } catch (error) {
      console.error("Error fetching streak information:", error);
    }
  };

  // Check if user has been away for a day or more and update streak
  const checkUserActivity = async () => {
    try {
      if (!state.user) return;

      // Get the last activity timestamp
      const lastActivityTime = localStorage.getItem("lastActivityTime");
      const now = new Date().getTime();

      // If no last activity or it's been more than 1 hour since last check
      if (!lastActivityTime || (now - parseInt(lastActivityTime)) > 3600000) { // 1 hour in milliseconds
        // Update the last activity time
        localStorage.setItem("lastActivityTime", now.toString());

        // Call the API to update the streak
        await customFetch.get('/user/update-streak');

        // Fetch updated gamification data
        await checkAndShowStreakPopup(state.user);
      }
    } catch (error) {
      console.error("Error checking user activity:", error);
    }
  };

  // Handle closing the streak popup
  const closeStreakPopup = () => {
    dispatch({ type: "SET_STREAK_POPUP", payload: false });
  };

  // Initialize user from localStorage and check activity
  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    const isAuthenticated = localStorage.getItem("isAuthenticated");

    if (storedUser && isAuthenticated === "true") {
      const user = JSON.parse(storedUser);
      dispatch({ type: "SET_USER", payload: user });

      // Check user activity and update streak if needed
      checkUserActivity();
    }
  }, []);

  // Add a visibility change listener to detect when user returns to the site
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && state.user) {
        // User has returned to the site
        checkUserActivity();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [state.user]);

  const updateUser = async (updates) => {
    try {
      dispatch({ type: "SET_LOADING", payload: true });

      // If updates is a FormData object, it's a profile update request
      if (updates instanceof FormData) {
        const { data } = await customFetch.patch(
          "/user/change-profile",
          updates,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        // Update localStorage with new user data
        if (data.user) {
          const updatedUser = { ...state.user, ...data.user };
          localStorage.setItem("user", JSON.stringify(updatedUser));
          dispatch({ type: "SET_USER", payload: updatedUser });
        }
      } else {
        // If updates is a regular object, it's a direct user update (e.g., from social login)
        dispatch({ type: "SET_USER", payload: updates });
      }

      return { success: true };
    } catch (error) {
      dispatch({
        type: "SET_ERROR",
        payload: error.response?.data?.msg || "Update failed",
      });

      return { error: error.response?.data?.msg || "Update failed" };
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  return (
    <UserContext.Provider
      value={{
        ...state,
        login,
        logout,
        updateUser,
        closeStreakPopup,
        checkUserActivity // Expose this function to be called from other components
      }}
    >
      {children}
      {state.showStreakPopup && state.streakInfo && (
        <StreakPopup
          streakCount={state.streakInfo.current}
          longestStreak={state.streakInfo.longest}
          onClose={closeStreakPopup}
        />
      )}
    </UserContext.Provider>
  );
};

// Custom hook for using the user context

export const useUser = () => {
  const context = useContext(UserContext);
  return context;
};
