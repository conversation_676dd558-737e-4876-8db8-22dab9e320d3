import { Outlet } from "react-router-dom";
import { Wrapper } from "../assets/styles/HomeLayout";
import { ScrollRestoration } from "react-router-dom";
import { Suspense, lazy, useEffect } from "react";
import LoadingSpinner from "@/components/loaders/LoadingSpinner";
import { useUser } from "@/context/UserContext";


// Lazy load components
const NavMenu = lazy(() => import("../components/homeComponents/HeaderComponents/NavMenu"));

const ChatLayout = () => {
  const { checkUserActivity } = useUser();

  // Check user activity when the layout mounts
  useEffect(() => {
    // Check for user activity and update streak if needed
    checkUserActivity();
  }, [checkUserActivity]);

  return (
    <Wrapper>
      <ScrollRestoration />
      <Suspense fallback={<LoadingSpinner />}>
        <NavMenu showMobile={false} />
      </Suspense>
      <Suspense fallback={<LoadingSpinner />}>
        <Outlet />
      </Suspense>
    </Wrapper>
  );
};

export default ChatLayout;
