import { Outlet, redirect } from "react-router-dom"
import { Wrapper } from "../assets/styles/HomeLayout"
import { ScrollRestoration } from "react-router-dom"
import customFetch from "@/utils/customFetch"
import { Suspense, lazy, useEffect } from "react"
import LoadingSpinner from "@/components/loaders/LoadingSpinner"
import { useUser } from "@/context/UserContext"

// Lazy load components
const NavMenu = lazy(() => import("../components/homeComponents/HeaderComponents/NavMenu"))
const Footer = lazy(() => import("../components/shared/Footer"))

// Memoize logout action
const logoutAction = async () => {
  try {
    const response = await customFetch.get("/auth/logout")
    if (response.status === 200) {
      return redirect("/")
    }
  } catch (error) {
    console.error("Logout error:", error)
  }
}
const HomeLayout = () => {
  const { checkUserActivity } = useUser();

  // Check user activity when the layout mounts
  useEffect(() => {
    // Check for user activity and update streak if needed
    checkUserActivity();
  }, [checkUserActivity]);

  return (
    <Wrapper>
      <ScrollRestoration />
      <Suspense fallback={<LoadingSpinner />}>
        <NavMenu />
      </Suspense>
      <Suspense fallback={<LoadingSpinner />}>
        <Outlet />
      </Suspense>
      <Suspense fallback={<LoadingSpinner />}>
        <Footer />
      </Suspense>
    </Wrapper>
  )
}

export { logoutAction }
export default HomeLayout