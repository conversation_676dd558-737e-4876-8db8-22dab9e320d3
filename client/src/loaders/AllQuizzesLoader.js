export const AllQuizzesLoader = async ({ request }) => {
    try {
        const url = new URL(request.url);
        const searchParams = Object.fromEntries(url.searchParams.entries());

        const {
            category: selectedCategory = "",
            search = "",
            difficulty = "",
            sortBy = "newest",
        } = searchParams;

        // Build query parameters for API
        const params = new URLSearchParams({
            isPublished: 'true',
            page: '1',
            limit: '50', // Get more quizzes for frontend filtering
        });

        if (selectedCategory) params.append('category', selectedCategory);
        if (search) params.append('search', search);
        if (difficulty) params.append('difficulty', difficulty);
        if (sortBy) params.append('sortBy', sortBy);

        const response = await fetch(`/api/v1/quizzes?${params}`, {
            credentials: 'include',
        });

        if (response.ok) {
            const data = await response.json();

            // Get all categories
            const categoriesResponse = await fetch('/api/v1/quizzes/meta/categories');
            let allCategories = [];
            if (categoriesResponse.ok) {
                const categoriesData = await categoriesResponse.json();
                allCategories = categoriesData.categories || [];
            }

            // Transform data to match the expected format
            const quizzesByCategory = {};
            data.quizzes.forEach(quiz => {
                if (!quizzesByCategory[quiz.category]) {
                    quizzesByCategory[quiz.category] = {
                        category: quiz.category,
                        quizzes: []
                    };
                }
                quizzesByCategory[quiz.category].quizzes.push(quiz);
            });

            const transformedData = Object.values(quizzesByCategory);

            return {
                data: transformedData,
                allCategories,
                totalQuizzes: data.totalQuizzes || 0,
                currentPage: data.currentPage || 1,
                totalPages: data.totalPages || 1
            };
        } else {
            console.error('Failed to fetch quizzes');
            return { data: [], allCategories: [] };
        }
    } catch (error) {
        console.error('Error fetching quizzes:', error);
        return { data: [], allCategories: [] };
    }
};