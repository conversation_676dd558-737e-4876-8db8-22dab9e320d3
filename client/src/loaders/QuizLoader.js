export const QuizLoader = async ({ params }) => {
    try {
        const { id } = params;
        
        if (!id) {
            throw new Error('Quiz ID is required');
        }

        const response = await fetch(`/api/v1/quizzes/${id}`, {
            credentials: 'include',
        });

        if (response.ok) {
            const data = await response.json();
            return data.quiz;
        } else if (response.status === 404) {
            throw new Error('Quiz not found');
        } else if (response.status === 401) {
            throw new Error('Quiz is not published or you do not have access');
        } else {
            throw new Error('Failed to fetch quiz');
        }
    } catch (error) {
        console.error('Error fetching quiz:', error);
        throw error;
    }
};
