import React from 'react';
import StreakPopup from '../components/notifications/StreakPopup';
import { useUser } from '../context/UserContext';
import customFetch from '@/utils/customFetch';
import { toast } from 'react-toastify';

const TestStreakPopup = () => {
  const [showPopup, setShowPopup] = React.useState(false);
  const [streakInfo, setStreakInfo] = React.useState({ current: 7, longest: 14 });
  const { checkUserActivity } = useUser();

  const fetchStreakInfo = async () => {
    try {
      const response = await customFetch.get('/user/gamification');
      if (response.data && response.data.streakProgress) {
        setStreakInfo({
          current: response.data.streakProgress.current,
          longest: response.data.streakProgress.longest
        });
        toast.success('Streak info fetched successfully');
      }
    } catch (error) {
      console.error('Error fetching streak info:', error);
      toast.error('Error fetching streak info');
    }
  };

  const updateStreak = async () => {
    try {
      await customFetch.get('/user/update-streak');
      toast.success('Streak updated successfully');
      fetchStreakInfo();
    } catch (error) {
      console.error('Error updating streak:', error);
      toast.error('Error updating streak');
    }
  };

  const resetLastStreakPopupDate = () => {
    localStorage.removeItem('lastStreakPopupDate');
    toast.info('Last streak popup date reset');
  };

  const triggerCheckActivity = async () => {
    try {
      await checkUserActivity();
      toast.success('Activity check triggered');
    } catch (error) {
      console.error('Error checking activity:', error);
      toast.error('Error checking activity');
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <h1 className="text-3xl font-bold mb-8">Test Streak Popup</h1>

      <div className="flex flex-col gap-4 mb-8">
        <div className="text-center">
          <p className="text-lg">Current Streak: <span className="font-bold">{streakInfo.current}</span></p>
          <p className="text-lg">Longest Streak: <span className="font-bold">{streakInfo.longest}</span></p>
        </div>

        <button
          onClick={fetchStreakInfo}
          className="px-6 py-3 bg-blue-500 text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
        >
          Fetch Streak Info
        </button>

        <button
          onClick={updateStreak}
          className="px-6 py-3 bg-green-500 text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
        >
          Update Streak
        </button>

        <button
          onClick={resetLastStreakPopupDate}
          className="px-6 py-3 bg-yellow-500 text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
        >
          Reset Last Popup Date
        </button>

        <button
          onClick={triggerCheckActivity}
          className="px-6 py-3 bg-purple-500 text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
        >
          Trigger Activity Check
        </button>
      </div>

      <button
        onClick={() => setShowPopup(true)}
        className="px-6 py-3 bg-gradient-to-r from-orange-400 to-pink-500 text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
      >
        Show Streak Popup
      </button>

      {showPopup && (
        <StreakPopup
          streakCount={streakInfo.current}
          longestStreak={streakInfo.longest}
          onClose={() => setShowPopup(false)}
        />
      )}
    </div>
  );
};

export default TestStreakPopup;
