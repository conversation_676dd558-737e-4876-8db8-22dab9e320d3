import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaUpload, FaArrowLeft, FaDownload, FaFileCode } from 'react-icons/fa';
import { useUser } from '../../context/UserContext';

const UploadQuizJson = () => {
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  
  const { user } = useUser();
  const navigate = useNavigate();

  const sampleQuizJson = {
    title: "Sample Mental Health Quiz",
    description: "A sample quiz to demonstrate the JSON format for bulk quiz creation",
    category: "Mental Health",
    subcategory: "Anxiety",
    difficulty: "intermediate",
    estimatedTime: 15,
    passingScore: 70,
    allowRetakes: true,
    showCorrectAnswers: true,
    randomizeQuestions: false,
    randomizeOptions: false,
    timeLimit: 20,
    tags: ["anxiety", "mental-health", "self-assessment"],
    questions: [
      {
        text: "How often do you feel anxious in social situations?",
        type: "multiple-choice",
        points: 2,
        difficulty: "medium",
        explanation: "Social anxiety is common and can be managed with proper techniques.",
        options: [
          {
            label: "Never",
            score: 0,
            isCorrect: false
          },
          {
            label: "Sometimes",
            score: 1,
            isCorrect: false
          },
          {
            label: "Often",
            score: 2,
            isCorrect: true
          },
          {
            label: "Always",
            score: 3,
            isCorrect: false
          }
        ]
      },
      {
        text: "Do you practice mindfulness or meditation?",
        type: "true-false",
        points: 1,
        difficulty: "easy",
        explanation: "Regular mindfulness practice can significantly reduce anxiety levels.",
        options: [
          {
            label: "Yes",
            score: 2,
            isCorrect: true
          },
          {
            label: "No",
            score: 0,
            isCorrect: false
          }
        ]
      }
    ]
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (selectedFile) => {
    if (selectedFile.type !== 'application/json') {
      toast.error('Please select a valid JSON file');
      return;
    }
    
    if (selectedFile.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('File size must be less than 5MB');
      return;
    }
    
    setFile(selectedFile);
    
    // Preview the JSON content
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target.result);
        setPreviewData(jsonData);
      } catch (error) {
        toast.error('Invalid JSON format');
        setFile(null);
        setPreviewData(null);
      }
    };
    reader.readAsText(selectedFile);
  };

  const handleFileInputChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const downloadSample = () => {
    const dataStr = JSON.stringify(sampleQuizJson, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = 'sample-quiz.json';
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!file) {
      toast.error('Please select a JSON file');
      return;
    }

    setLoading(true);
    
    try {
      const formData = new FormData();
      formData.append('quizFile', file);

      const response = await fetch('/api/v1/quizzes/upload-json', {
        method: 'POST',
        credentials: 'include',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Quiz created successfully from JSON!');
        navigate('/admin/quiz-management');
      } else {
        const error = await response.json();
        toast.error(error.msg || 'Failed to create quiz from JSON');
      }
    } catch (error) {
      console.error('Error uploading quiz JSON:', error);
      toast.error('Error uploading quiz JSON');
    } finally {
      setLoading(false);
    }
  };

  if (!user || (user.role !== 'admin' && user.role !== 'contributor')) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8 flex items-center">
          <button
            onClick={() => navigate('/admin/quiz-management')}
            className="mr-4 p-2 text-gray-600 hover:text-gray-900"
          >
            <FaArrowLeft />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Upload Quiz JSON</h1>
            <p className="mt-2 text-gray-600">Create quizzes by uploading a JSON file</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Section */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Upload JSON File</h2>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* File Drop Zone */}
              <div
                className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive
                    ? 'border-blue-400 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  accept=".json"
                  onChange={handleFileInputChange}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                
                <div className="space-y-4">
                  <FaFileCode className="mx-auto h-12 w-12 text-gray-400" />
                  <div>
                    <p className="text-lg font-medium text-gray-900">
                      {file ? file.name : 'Drop your JSON file here'}
                    </p>
                    <p className="text-sm text-gray-500">
                      or click to browse (max 5MB)
                    </p>
                  </div>
                </div>
              </div>

              {/* File Info */}
              {file && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <FaFileCode className="h-5 w-5 text-green-400 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-green-800">
                        {file.name}
                      </p>
                      <p className="text-sm text-green-600">
                        {(file.size / 1024).toFixed(2)} KB
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={!file || loading}
                className="w-full inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FaUpload className="mr-2" />
                {loading ? 'Uploading...' : 'Create Quiz from JSON'}
              </button>
            </form>
          </div>

          {/* Instructions and Sample */}
          <div className="space-y-6">
            {/* Instructions */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Instructions</h2>
              
              <div className="space-y-4 text-sm text-gray-600">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Required Fields:</h3>
                  <ul className="list-disc list-inside space-y-1">
                    <li>title (string)</li>
                    <li>description (string)</li>
                    <li>category (string)</li>
                    <li>questions (array with at least 1 question)</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Question Format:</h3>
                  <ul className="list-disc list-inside space-y-1">
                    <li>text (required)</li>
                    <li>options (array with at least 2 options)</li>
                    <li>Each option needs: label, score</li>
                    <li>Optional: type, points, difficulty, explanation</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Tips:</h3>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Use the sample JSON as a template</li>
                    <li>Validate your JSON before uploading</li>
                    <li>File size limit: 5MB</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Sample Download */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Sample JSON</h2>
              <p className="text-sm text-gray-600 mb-4">
                Download a sample JSON file to use as a template for your quiz.
              </p>
              
              <button
                onClick={downloadSample}
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                <FaDownload className="mr-2" />
                Download Sample
              </button>
            </div>

            {/* Preview */}
            {previewData && (
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Preview</h2>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Title:</span> {previewData.title}</p>
                  <p><span className="font-medium">Category:</span> {previewData.category}</p>
                  <p><span className="font-medium">Questions:</span> {previewData.questions?.length || 0}</p>
                  <p><span className="font-medium">Difficulty:</span> {previewData.difficulty || 'Not specified'}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UploadQuizJson;
