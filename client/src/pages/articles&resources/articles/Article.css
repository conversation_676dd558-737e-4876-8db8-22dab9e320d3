/* Font imports */
@import url("https://fonts.googleapis.com/css2?family=Source+Serif+Pro:ital,wght@0,400;0,600;0,700;1,400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700&display=swap");

.article-body {
  font-family: "Lora", "Source Serif Pro", Georgia, "Times New Roman", serif;
  font-size: 1.25rem;
  line-height: 1.9;
  color: var(--grey-800);
  overflow: hidden;
}

.article-body h1,
.article-body h2,
.article-body h3,
.article-body h4 {
  font-family:
    "Inter",
    system-ui,
    -apple-system,
    sans-serif;
  letter-spacing: -0.02em;
  color: #1a202c;
}

.article-body blockquote {
  font-family: "Merriweather", "Source Serif Pro", Georgia, serif;
  font-style: italic;
  border-left: 4px solid #3b82f6;
  padding-left: 1.5rem;
  margin: 2rem 0;
  color: #4a5568;
}

.article-meta,
.meta-item,
.article-tag {
  font-family:
    "Inter",
    system-ui,
    -apple-system,
    sans-serif;
}

.article-category {
  font-family:
    "Inter",
    system-ui,
    -apple-system,
    sans-serif;
  letter-spacing: 0.05em;
}
.article-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0.5rem;
  background: var(--pure);
  position: relative;
}

.article-content {
  background: var(--pure);
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 3rem;
  position: relative;
  overflow: hidden;
}

.article-header {
  text-align: center;
  margin-bottom: 1rem;
  position: relative;
}

.article-category {
  display: inline-block;
  background: rgba(66, 153, 225, 0.1);
  color: #4299e1;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--grey-600);
  margin-bottom: 2rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.meta-item img {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
}

.meta-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--grey-500);
}

.meta-separator {
  width: 4px;
  height: 4px;
  background-color: var(--grey-400);
  border-radius: 50%;
}

.progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  height: 4px; /* Adjusted height for better visibility */
  background-color: var(--primary); /* Distinct background color */
  z-index: 1000; /* Ensure it stays on top of other elements */
  transition: width 0.1s ease; /* Smooth transition */
}

.table-of-contents {
  position: sticky;
  top: 20px;
  z-index: 1000;
}

/* Base typography settings */
.article-body {
  color: #374151;
  font-size: 1.25rem;
  line-height: 1.9;
  font-family: "Lora", "Merriweather", Georgia, serif;
  max-width: 75ch;
  margin: 0 auto;
}

/* Enhanced heading hierarchy with decorative elements */
.article-body h1 {
  font-size: clamp(28px, 5vw, 42px);
  font-weight: 800;
  color: #1a202c;
  margin: 3.5rem 0 1.75rem;
  scroll-margin-top: 100px;
  letter-spacing: -0.03em;
  line-height: 1.2;
  position: relative;
  padding-bottom: 0.5rem;
}

.article-body h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 2px;
}

.article-body h2 {
  font-size: clamp(24px, 4vw, 36px);
  font-weight: 700;
  color: #1f2937;
  margin: 3rem 0 1.5rem;
  scroll-margin-top: 100px;
  letter-spacing: -0.02em;
  line-height: 1.3;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.75rem;
  position: relative;
}

.article-body h2::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background-color: #3b82f6;
}

.article-body h3 {
  font-size: clamp(20px, 3vw, 28px);
  font-weight: 700;
  color: #1f2937;
  margin: 2.5rem 0 1.25rem;
  scroll-margin-top: 100px;
  line-height: 1.4;
  display: inline-block;
  position: relative;
}

.article-body h3::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.5), rgba(59, 130, 246, 0));
  border-radius: 1px;
}

.article-body h4 {
  font-size: clamp(18px, 2.5vw, 22px);
  font-weight: 600;
  color: #374151;
  margin: 2rem 0 1rem;
  scroll-margin-top: 100px;
  line-height: 1.4;
}

/* Refined paragraph styling with drop cap for first paragraph */
.article-body > p:first-of-type {
  font-size: 1.2rem;
  line-height: 1.9;
  color: #1f2937;
}

.article-body > p:first-of-type::first-letter {
  initial-letter: 2;
  -webkit-initial-letter: 2;
  color: #3b82f6;
  font-weight: 600;
  margin-right: 0.5rem;
  font-family: "Merriweather", serif;
}

.article-body p {
  margin: 0 0 1.5rem 0;
  color: #4b5563;
  font-size: 1.125rem;
  line-height: 1.85;
}

/* Enhanced link styling with animated underline */
.article-body a {
  color: #2563eb;
  text-decoration: none;
  position: relative;
  font-weight: 500;
  transition: color 0.2s ease;
}

.article-body a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #3b82f6;
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.article-body a:hover {
  color: #1d4ed8;
}

.article-body a:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.article-body a:hover {
  border-bottom-color: #2563eb;
  color: #1d4ed8;
  background-color: rgba(37, 99, 235, 0.05);
}

.article-body ul,
.article-body ol {
  margin: 1.75rem 0;
  padding-left: 2.5rem;
  font-family: "Lora", Georgia, serif;
  color: #4b5563;
}

.article-body li {
  padding-left: 0px;
  position: relative;
  margin-bottom: 0.5rem;
  line-height: 1.75;
}

/* Preserve list style from rich text editor */
.article-body ol[style*="list-style-type"],
.article-body ul[style*="list-style-type"] {
  list-style-type: inherit;
}

.article-body ol[style*="list-style-type"] li,
.article-body ul[style*="list-style-type"] li {
  list-style-type: inherit;
}

.article-body li::marker {
  color: var(--primary);
}

/* Enhanced strong text */
.article-body strong {
  color: #1a202c;
  font-weight: 600;
  background: linear-gradient(to bottom, transparent 50%, rgba(59, 130, 246, 0.1) 50%);
}

/* Refined blockquote styling with decorative elements */
.article-body blockquote {
  margin: 2.5rem 0;
  padding: 1.5rem 2rem;
  border-radius: 0.5rem;
  background: rgba(37, 99, 235, 0.05);
  border-left: 4px solid #3b82f6;
  font-style: italic;
  color: #4b5563;
  position: relative;
}

.article-body blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 3rem;
  color: rgba(59, 130, 246, 0.2);
  font-family: Georgia, serif;
}

.article-body blockquote p {
  font-size: 1.125rem;
  line-height: 1.75;
  position: relative;
  z-index: 1;
}

.article-body blockquote p:last-child {
  margin-bottom: 0;
}

/* Code block styling with improved readability */
.article-body pre {
  background: #1e293b;
  color: #e2e8f0;
  border-radius: 0.75rem;
  overflow-x: auto;
  margin: 2rem 0;
  padding: 1.5rem;
  font-size: 0.95em;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.article-body code {
  font-family: 'Fira Code', 'Courier New', monospace;
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-size: 0.9em;
}

.article-body pre code {
  background-color: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

.article-body code {
  font-family: "Fira Code", monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background: rgba(37, 99, 235, 0.1);
  border-radius: 0.25rem;
  color: #2563eb;
}

/* Image enhancement */
.article-body img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 2.5rem 0;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.article-tag {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: var(--grey--100);
  color: var(--grey--700);
  border-radius: 2rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.article-tag:hover {
  background: var(--grey--200);
  color: var(--grey--900);
}

/* Like button styles */
.like-button {
  cursor: pointer;
  transition: transform 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.like-button:hover {
  transform: scale(1.1);
}

.like-button .meta-icon.liked {
  color: #e74c3c;
}

.like-button .meta-icon {
  font-size: 1.2rem;
}

/* Loading state */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Error state */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  text-align: center;
  padding: 2rem;
}

.error-icon {
  color: #e53e3e;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-message {
  color: var(--grey--700);
  font-size: 1.125rem;
  max-width: 500px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .article-container {
    padding: 1rem;
  }

  .article-content {
    padding: 2rem 1.5rem;
    border-radius: 0.75rem;
  }

  .article-header h1 {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  .article-body h1 {
    font-size: 1.325rem;
    margin-bottom: 1rem;
  }

  .article-meta {
    gap: 0.75rem;
  }

  .meta-separator {
    display: none;
  }

  .article-body {
    font-size: 1rem;
  }

  .article-body h2 {
    font-size: 1.75rem;
  }

  .article-body h3 {
    font-size: 1.375rem;
  }

  .article-body blockquote {
    padding: 1.5rem;
    margin: 2rem 0;
  }
  .table-of-contents {
    position: relative;
  }
}

/* Print styles */
@media print {
  .article-container {
    max-width: none;
    margin: 0;
    padding: 0;
  }

  .article-content {
    box-shadow: none;
    padding: 0;
  }

  .article-body {
    font-size: 12pt;
  }

  .article-body a {
    text-decoration: underline;
    color: #000;
  }

  .article-footer {
    display: none;
  }
}
