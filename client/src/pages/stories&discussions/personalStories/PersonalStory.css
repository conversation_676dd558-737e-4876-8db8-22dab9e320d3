/* Progress bar */
.progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(to right, var(--primary), var(--ternery));
  z-index: 100;
  transition: width 0.2s ease;
}

/* Article body styling */
.article-body h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: var(--grey--900);
}

.article-body h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--grey--800);
}

.article-body h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--grey--800);
}

.article-body p {
  margin-bottom: 1rem;
  line-height: 1.7;
}

.article-body a {
  color: var(--primary);
  text-decoration: underline;
}

.article-body ul, .article-body ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.article-body li {
  margin-bottom: 0.5rem;
}

.article-body blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: var(--grey--700);
}

.article-body img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
}

.article-body pre {
  background-color: var(--grey--100);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.article-body code {
  background-color: var(--grey--100);
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-family: monospace;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Meta item styling */
.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.meta-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.meta-icon.liked {
  color: var(--ternery);
}

/* Table of contents */
.table-of-contents {
  position: relative;
}

.toc-nav {
  position: relative;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .article-body h1 {
    font-size: 1.75rem;
  }
  
  .article-body h2 {
    font-size: 1.35rem;
  }
  
  .article-body h3 {
    font-size: 1.15rem;
  }
}
