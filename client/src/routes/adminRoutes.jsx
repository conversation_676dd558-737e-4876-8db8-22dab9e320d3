import React from 'react';
import AdminOutlet from '../pages/admin/AdminOutlet';
import Inbox from '../pages/admin/Inbox';
import UserSuggestions from '../pages/admin/UserSuggestions';
import QuizManagement from '../pages/admin/QuizManagement';
import CreateQuiz from '../pages/admin/CreateQuiz';
import UploadQuizJson from '../pages/admin/UploadQuizJson';

export const adminRoutes = {
    path: '/admin',
    element: <AdminOutlet />,
    children: [
        {
            index: true,
            element: <Inbox />
        },
        {
            path: 'suggestions',
            element: <UserSuggestions />
        },
        {
            path: 'quiz-management',
            element: <QuizManagement />
        },
        {
            path: 'quiz/create',
            element: <CreateQuiz />
        },
        {
            path: 'quiz/upload',
            element: <UploadQuizJson />
        }
    ]
};
