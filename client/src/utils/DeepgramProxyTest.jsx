import React, { useState } from 'react';
import axios from 'axios';

const DeepgramProxyTest = () => {
  const [testResult, setTestResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const testConnection = async () => {
    setIsLoading(true);
    setError(null);
    setTestResult(null);
    
    try {
      // Test the connection to the Deepgram API via our proxy
      const response = await axios.get('/api/v1/tts/test');
      setTestResult(response.data);
    } catch (err) {
      console.error('Error testing Deepgram connection:', err);
      setError(err.response?.data || { error: err.message });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
      <h2>Deepgram Proxy Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <p>
          This component tests the connection to the Deepgram API via the server-side proxy.
          Click the button below to test if the server can successfully connect to Deepgram.
        </p>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={testConnection}
          disabled={isLoading}
          style={{
            padding: '10px 20px',
            backgroundColor: '#4a6cf7',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            opacity: isLoading ? 0.7 : 1
          }}
        >
          {isLoading ? 'Testing...' : 'Test Deepgram Connection'}
        </button>
      </div>
      
      {error && (
        <div style={{ 
          color: 'white', 
          backgroundColor: '#ff4d4f', 
          padding: '15px', 
          borderRadius: '5px',
          marginBottom: '20px'
        }}>
          <h3>Error</h3>
          <pre style={{ 
            whiteSpace: 'pre-wrap', 
            wordBreak: 'break-word',
            backgroundColor: 'rgba(0,0,0,0.1)',
            padding: '10px',
            borderRadius: '3px'
          }}>
            {JSON.stringify(error, null, 2)}
          </pre>
        </div>
      )}
      
      {testResult && (
        <div style={{ 
          color: 'white', 
          backgroundColor: '#52c41a', 
          padding: '15px', 
          borderRadius: '5px',
          marginBottom: '20px'
        }}>
          <h3>Success</h3>
          <pre style={{ 
            whiteSpace: 'pre-wrap', 
            wordBreak: 'break-word',
            backgroundColor: 'rgba(0,0,0,0.1)',
            padding: '10px',
            borderRadius: '3px'
          }}>
            {JSON.stringify(testResult, null, 2)}
          </pre>
        </div>
      )}
      
      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
        <h3>Troubleshooting Tips</h3>
        <ol style={{ paddingLeft: '20px' }}>
          <li>Make sure the server is running</li>
          <li>Check that the Deepgram API key is correctly set in the server's .env file</li>
          <li>Verify that the @deepgram/sdk package is installed on the server</li>
          <li>Check the server logs for any errors</li>
        </ol>
      </div>
    </div>
  );
};

export default DeepgramProxyTest;
