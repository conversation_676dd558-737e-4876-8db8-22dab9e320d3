import React, { useState } from 'react';
import { textToSpeech } from './deepgramTTS';

const DeepgramTTSTestV3 = () => {
  const [text, setText] = useState('Hello, this is a test of the Deepgram text-to-speech functionality.');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [audioUrl, setAudioUrl] = useState(null);
  const [selectedVoice, setSelectedVoice] = useState('aura-2-thalia-en');

  const voices = [
    { id: 'aura-2-thalia-en', name: '<PERSON>ra 2 T<PERSON><PERSON> (Female)' },
    { id: 'aura-1', name: '<PERSON>ra 1 (Female)' },
    { id: 'aura-asteria-en', name: '<PERSON><PERSON> (Female)' },
    { id: 'nova-asteria-en', name: '<PERSON> Asteria (Male)' }
  ];

  const handleTextToSpeech = async () => {
    if (!text.trim()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // No need for API key as we're using the server-side proxy
      const result = await textToSpeech(text, {
        voice: selectedVoice
      });

      // Store the audio URL
      setAudioUrl(result.audioUrl);

      // Play the audio
      result.play();
    } catch (err) {
      console.error('Error in TTS test:', err);
      setError(err.message || 'An error occurred during text-to-speech conversion');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
      <h2>Deepgram TTS Test (v3 SDK)</h2>

      <div style={{ marginBottom: '20px' }}>
        <label htmlFor="voice-select" style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
          Select Voice:
        </label>
        <select
          id="voice-select"
          value={selectedVoice}
          onChange={(e) => setSelectedVoice(e.target.value)}
          style={{
            width: '100%',
            padding: '10px',
            borderRadius: '5px',
            border: '1px solid #ccc',
            marginBottom: '16px'
          }}
        >
          {voices.map(voice => (
            <option key={voice.id} value={voice.id}>
              {voice.name}
            </option>
          ))}
        </select>

        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Enter text to convert to speech..."
          style={{
            width: '100%',
            padding: '10px',
            borderRadius: '5px',
            border: '1px solid #ccc',
            minHeight: '100px',
            fontFamily: 'inherit'
          }}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={handleTextToSpeech}
          disabled={isLoading || !text.trim()}
          style={{
            padding: '10px 20px',
            backgroundColor: '#4a6cf7',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: isLoading || !text.trim() ? 'not-allowed' : 'pointer',
            opacity: isLoading || !text.trim() ? 0.7 : 1
          }}
        >
          {isLoading ? 'Converting...' : 'Convert to Speech'}
        </button>
      </div>

      {error && (
        <div style={{ color: 'red', marginBottom: '20px', padding: '10px', backgroundColor: '#ffeeee', borderRadius: '5px' }}>
          <p><strong>Error:</strong> {error}</p>
        </div>
      )}

      {audioUrl && !error && (
        <div style={{ marginTop: '20px' }}>
          <h3>Audio Preview</h3>
          <audio controls src={audioUrl} style={{ width: '100%' }} />
        </div>
      )}

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
        <h3>Instructions</h3>
        <ol style={{ paddingLeft: '20px' }}>
          <li>Select a voice from the dropdown</li>
          <li>Enter some text in the textarea above</li>
          <li>Click "Convert to Speech" to test the Deepgram TTS functionality</li>
          <li>Make sure you have added your Deepgram API key to the server's .env file as <code>DEEPGRAM_API_KEY</code></li>
        </ol>
      </div>
    </div>
  );
};

export default DeepgramTTSTestV3;
