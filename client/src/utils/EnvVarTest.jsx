import React, { useEffect, useState } from 'react';

const EnvVarTest = () => {
  const [envVars, setEnvVars] = useState([]);
  const [apiKeyStatus, setApiKeyStatus] = useState('Checking...');

  useEffect(() => {
    // Get all environment variables that start with VITE_
    // For Vite projects, environment variables are accessed via import.meta.env
    const viteVars = Object.keys(import.meta.env)
      .filter(key => key.startsWith('VITE_'))
      .map(key => ({
        name: key,
        // Don't show actual values for security, just whether they exist
        value: import.meta.env[key] ? '[Value exists]' : '[No value]'
      }));

    setEnvVars(viteVars);

    // Check specifically for the Deepgram API key
    const hasDeepgramKey = Boolean(import.meta.env.VITE_DEEPGRAM_API_KEY);
    setApiKeyStatus(hasDeepgramKey ? 'API Key is present' : 'API Key is missing');
  }, []);

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <h2>Environment Variables Test</h2>

      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f0f4ff', borderRadius: '5px' }}>
        <h3>Deepgram API Key Status</h3>
        <p style={{
          color: apiKeyStatus.includes('missing') ? 'red' : 'green',
          fontWeight: 'bold'
        }}>
          {apiKeyStatus}
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Available Vite Environment Variables</h3>
        {envVars.length === 0 ? (
          <p>No VITE_ environment variables found.</p>
        ) : (
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr>
                <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>Variable Name</th>
                <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>Status</th>
              </tr>
            </thead>
            <tbody>
              {envVars.map((variable, index) => (
                <tr key={index}>
                  <td style={{ padding: '8px', borderBottom: '1px solid #ddd' }}>{variable.name}</td>
                  <td style={{ padding: '8px', borderBottom: '1px solid #ddd' }}>{variable.value}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
        <h3>Troubleshooting Tips</h3>
        <ol style={{ paddingLeft: '20px' }}>
          <li>Environment variables in Vite must start with <code>VITE_</code> (not REACT_APP_)</li>
          <li>Make sure your <code>.env</code> file is in the client directory (not the root)</li>
          <li>There should be no spaces around the equals sign in the <code>.env</code> file</li>
          <li>After changing the <code>.env</code> file, you need to restart the development server</li>
          <li>The <code>.env</code> file should not be in quotes or have trailing semicolons</li>
          <li>For Vite projects, use <code>VITE_DEEPGRAM_API_KEY=your_key</code> in your .env file</li>
        </ol>
      </div>
    </div>
  );
};

export default EnvVarTest;
