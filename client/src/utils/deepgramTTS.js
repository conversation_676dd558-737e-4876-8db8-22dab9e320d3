import axios from 'axios';

// Convert text to speech using our server-side proxy to Deepgram's API
export const textToSpeech = async (text, options = {}) => {
  try {
    console.log('Converting text to speech via proxy endpoint');

    // Default TTS options
    const ttsOptions = {
      text,
      voice: options.voice || 'aura-2-thalia-en', // Default voice/model
      model: options.model || 'aura-2-thalia-en', // Using a more widely available model
    };

    // Make the API request to our server-side proxy
    const response = await axios.post('/api/v1/tts/convert', ttsOptions, {
      responseType: 'arraybuffer' // Important: we need the raw binary data
    });

    // Convert the response data to a Blob
    const audioBlob = new Blob([response.data], { type: 'audio/mp3' });

    // Create a URL for the audio blob
    const audioUrl = URL.createObjectURL(audioBlob);

    return {
      audioUrl,
      audioBlob,
      play: () => {
        const audio = new Audio(audioUrl);
        return audio.play();
      },
      cleanup: () => {
        URL.revokeObjectURL(audioUrl);
      }
    };
  } catch (error) {
    console.error('Error in Deepgram TTS:', error);
    throw error;
  }
};

// Helper function to speak text using Deepgram
export const speakWithDeepgram = async (text, options = {}) => {
  try {
    const result = await textToSpeech(text, options);
    return result.play();
  } catch (error) {
    console.error('Failed to speak with Deepgram:', error);
    throw error;
  }
};

export default {
  textToSpeech,
  speakWithDeepgram
};
