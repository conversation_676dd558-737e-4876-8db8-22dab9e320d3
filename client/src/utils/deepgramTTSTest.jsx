import React, { useState } from 'react';
import { textToSpeech } from './deepgramTTS';

const DeepgramTTSTest = () => {
  const [text, setText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [audioUrl, setAudioUrl] = useState(null);

  const handleTextToSpeech = async () => {
    if (!text.trim()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get API key from environment variable (using Vite's approach)
      const apiKey = import.meta.env.VITE_DEEPGRAM_API_KEY;

      if (!apiKey) {
        throw new Error('Deepgram API key is missing. Please add VITE_DEEPGRAM_API_KEY to your .env file.');
      }

      const result = await textToSpeech(text, { apiKey });

      // Store the audio URL
      setAudioUrl(result.audioUrl);

      // Play the audio
      result.play();
    } catch (err) {
      console.error('Error in TTS test:', err);
      setError(err.message || 'An error occurred during text-to-speech conversion');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
      <h2>Deepgram TTS Test</h2>

      <div style={{ marginBottom: '20px' }}>
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Enter text to convert to speech..."
          style={{
            width: '100%',
            padding: '10px',
            borderRadius: '5px',
            border: '1px solid #ccc',
            minHeight: '100px',
            fontFamily: 'inherit'
          }}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={handleTextToSpeech}
          disabled={isLoading || !text.trim()}
          style={{
            padding: '10px 20px',
            backgroundColor: '#4a6cf7',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: isLoading || !text.trim() ? 'not-allowed' : 'pointer',
            opacity: isLoading || !text.trim() ? 0.7 : 1
          }}
        >
          {isLoading ? 'Converting...' : 'Convert to Speech'}
        </button>
      </div>

      {error && (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          <p><strong>Error:</strong> {error}</p>
        </div>
      )}

      {audioUrl && !error && (
        <div style={{ marginTop: '20px' }}>
          <h3>Audio Preview</h3>
          <audio controls src={audioUrl} style={{ width: '100%' }} />
        </div>
      )}

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
        <h3>Instructions</h3>
        <ol style={{ paddingLeft: '20px' }}>
          <li>Enter some text in the textarea above</li>
          <li>Click "Convert to Speech" to test the Deepgram TTS functionality</li>
          <li>Make sure you have added your Deepgram API key to the .env file</li>
        </ol>
      </div>
    </div>
  );
};

export default DeepgramTTSTest;
