import Quiz from "../models/quizzes/quizModel.js";
import QuizAttempt from "../models/quizzes/quizAttemptModel.js";
import User from "../models/userModel.js";
import { StatusCodes } from "http-status-codes";
import { BadRequestError, NotFoundError, UnauthorizedError } from "../errors/customErors.js";
import mongoose from "mongoose";
import { updateUserPoints, awardBadges } from "../utils/gamification.js";

// Create a new quiz
export const createQuiz = async (req, res) => {
  const { userId } = req.user;
  
  try {
    const quizData = {
      ...req.body,
      createdBy: userId,
    };
    
    // Generate unique questionIds if not provided
    if (quizData.questions) {
      quizData.questions = quizData.questions.map((question, index) => ({
        ...question,
        questionId: question.questionId || `q_${Date.now()}_${index}`,
        order: question.order || index + 1,
      }));
    }
    
    const quiz = await Quiz.create(quizData);
    
    // Award points to creator
    await updateUserPoints(userId, "quiz_creation");
    const earnedBadges = await awardBadges(userId, "quiz_creation");
    
    res.status(StatusCodes.CREATED).json({
      msg: "Quiz created successfully",
      quiz,
      earnedBadges,
    });
  } catch (error) {
    console.error("Error creating quiz:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error creating quiz",
      error: error.message,
    });
  }
};

// Create quiz from JSON upload
export const createQuizFromJson = async (req, res) => {
  const { userId } = req.user;
  
  try {
    const quizData = {
      ...req.quizData, // Parsed JSON data from middleware
      createdBy: userId,
    };
    
    // Generate unique questionIds and order
    if (quizData.questions) {
      quizData.questions = quizData.questions.map((question, index) => ({
        ...question,
        questionId: question.questionId || `q_${Date.now()}_${index}`,
        order: question.order || index + 1,
      }));
    }
    
    const quiz = await Quiz.create(quizData);
    
    // Award points to creator
    await updateUserPoints(userId, "quiz_creation");
    const earnedBadges = await awardBadges(userId, "quiz_creation");
    
    res.status(StatusCodes.CREATED).json({
      msg: "Quiz created successfully from JSON",
      quiz,
      earnedBadges,
    });
  } catch (error) {
    console.error("Error creating quiz from JSON:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error creating quiz from JSON",
      error: error.message,
    });
  }
};

// Get all quizzes with filtering and pagination
export const getAllQuizzes = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const {
      category,
      difficulty,
      search,
      sortBy = "newest",
      isPublished = "true",
    } = req.query;
    
    // Build filter object
    const filter = { isActive: true };
    
    if (isPublished === "true") {
      filter.isPublished = true;
    }
    
    if (category) {
      filter.category = new RegExp(category, "i");
    }
    
    if (difficulty) {
      filter.difficulty = difficulty;
    }
    
    if (search) {
      filter.$or = [
        { title: new RegExp(search, "i") },
        { description: new RegExp(search, "i") },
        { tags: { $in: [new RegExp(search, "i")] } },
      ];
    }
    
    // Build sort object
    let sort = {};
    switch (sortBy) {
      case "newest":
        sort = { createdAt: -1 };
        break;
      case "oldest":
        sort = { createdAt: 1 };
        break;
      case "popular":
        sort = { "stats.totalAttempts": -1 };
        break;
      case "title":
        sort = { title: 1 };
        break;
      default:
        sort = { createdAt: -1 };
    }
    
    const [quizzes, totalQuizzes] = await Promise.all([
      Quiz.find(filter)
        .populate("createdBy", "name avatar")
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Quiz.countDocuments(filter),
    ]);
    
    const totalPages = Math.ceil(totalQuizzes / limit);
    
    res.status(StatusCodes.OK).json({
      quizzes,
      currentPage: page,
      totalPages,
      totalQuizzes,
      hasMore: page < totalPages,
    });
  } catch (error) {
    console.error("Error fetching quizzes:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error fetching quizzes",
      error: error.message,
    });
  }
};

// Get single quiz by ID
export const getQuizById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const quiz = await Quiz.findById(id)
      .populate("createdBy", "name avatar role")
      .lean();
    
    if (!quiz) {
      throw new NotFoundError("Quiz not found");
    }
    
    // Check if quiz is published or if user is the creator/admin
    if (!quiz.isPublished && (!req.user || (req.user.userId !== quiz.createdBy._id.toString() && req.user.role !== "admin"))) {
      throw new UnauthorizedError("Quiz is not published");
    }
    
    res.status(StatusCodes.OK).json({ quiz });
  } catch (error) {
    console.error("Error fetching quiz:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error fetching quiz",
      error: error.message,
    });
  }
};

// Update quiz
export const updateQuiz = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, role } = req.user;
    
    const quiz = await Quiz.findById(id);
    
    if (!quiz) {
      throw new NotFoundError("Quiz not found");
    }
    
    // Check if user is the creator or admin
    if (quiz.createdBy.toString() !== userId && role !== "admin") {
      throw new UnauthorizedError("Not authorized to update this quiz");
    }
    
    // Update questions with proper IDs if provided
    if (req.body.questions) {
      req.body.questions = req.body.questions.map((question, index) => ({
        ...question,
        questionId: question.questionId || `q_${Date.now()}_${index}`,
        order: question.order || index + 1,
      }));
    }
    
    const updatedQuiz = await Quiz.findByIdAndUpdate(
      id,
      { ...req.body, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).populate("createdBy", "name avatar");
    
    res.status(StatusCodes.OK).json({
      msg: "Quiz updated successfully",
      quiz: updatedQuiz,
    });
  } catch (error) {
    console.error("Error updating quiz:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error updating quiz",
      error: error.message,
    });
  }
};

// Delete quiz
export const deleteQuiz = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, role } = req.user;
    
    const quiz = await Quiz.findById(id);
    
    if (!quiz) {
      throw new NotFoundError("Quiz not found");
    }
    
    // Check if user is the creator or admin
    if (quiz.createdBy.toString() !== userId && role !== "admin") {
      throw new UnauthorizedError("Not authorized to delete this quiz");
    }
    
    // Soft delete by setting isActive to false
    await Quiz.findByIdAndUpdate(id, { isActive: false });
    
    res.status(StatusCodes.OK).json({
      msg: "Quiz deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting quiz:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error deleting quiz",
      error: error.message,
    });
  }
};

// Get quizzes created by a specific user
export const getUserQuizzes = async (req, res) => {
  try {
    const { userId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [quizzes, totalQuizzes] = await Promise.all([
      Quiz.find({ createdBy: userId, isActive: true })
        .populate("createdBy", "name avatar")
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Quiz.countDocuments({ createdBy: userId, isActive: true }),
    ]);

    const totalPages = Math.ceil(totalQuizzes / limit);

    res.status(StatusCodes.OK).json({
      quizzes,
      currentPage: page,
      totalPages,
      totalQuizzes,
      hasMore: page < totalPages,
    });
  } catch (error) {
    console.error("Error fetching user quizzes:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error fetching user quizzes",
      error: error.message,
    });
  }
};

// Submit quiz attempt
export const submitQuizAttempt = async (req, res) => {
  try {
    const { id: quizId } = req.params;
    const { userId } = req.user;
    const { responses, timeSpent = 0 } = req.body;

    const quiz = await Quiz.findById(quizId);

    if (!quiz) {
      throw new NotFoundError("Quiz not found");
    }

    if (!quiz.isPublished || !quiz.isActive) {
      throw new BadRequestError("Quiz is not available for attempts");
    }

    // Check if retakes are allowed
    const existingAttempts = await QuizAttempt.countDocuments({
      userId,
      quizId,
      isCompleted: true,
    });

    if (existingAttempts > 0 && !quiz.allowRetakes) {
      throw new BadRequestError("Retakes are not allowed for this quiz");
    }

    // Calculate score and validate responses
    let totalScore = 0;
    const processedResponses = [];

    for (const response of responses) {
      const question = quiz.questions.find(q => q.questionId === response.questionId);

      if (!question) {
        throw new BadRequestError(`Invalid question ID: ${response.questionId}`);
      }

      const selectedOption = question.options.find(opt => opt.value === response.selectedOption);

      if (!selectedOption) {
        throw new BadRequestError(`Invalid option for question: ${response.questionId}`);
      }

      const pointsEarned = selectedOption.score || 0;
      totalScore += pointsEarned;

      processedResponses.push({
        questionId: response.questionId,
        selectedOption: response.selectedOption,
        isCorrect: selectedOption.isCorrect || false,
        pointsEarned,
        timeSpent: response.timeSpent || 0,
      });
    }

    // Calculate percentage and determine if passed
    const percentage = Math.round((totalScore / quiz.totalPoints) * 100);
    const passed = percentage >= quiz.passingScore;

    // Find assessment result based on score
    let assessmentResult = null;
    if (quiz.assessments && quiz.assessments.length > 0) {
      assessmentResult = quiz.assessments.find(
        assessment => totalScore >= assessment.minScore && totalScore <= assessment.maxScore
      );
    }

    // Create quiz attempt
    const attemptData = {
      userId,
      quizId,
      responses: processedResponses,
      totalScore,
      maxPossibleScore: quiz.totalPoints,
      percentage,
      passed,
      timeSpent,
      completedAt: new Date(),
      isCompleted: true,
      attemptNumber: existingAttempts + 1,
      assessmentResult,
    };

    const quizAttempt = await QuizAttempt.create(attemptData);

    // Update quiz statistics
    await updateQuizStats(quizId);

    // Award points for quiz completion
    if (passed) {
      await updateUserPoints(userId, "quiz_completion");
      const earnedBadges = await awardBadges(userId, "quiz_completion");

      res.status(StatusCodes.CREATED).json({
        msg: "Quiz completed successfully!",
        attempt: quizAttempt,
        passed,
        earnedBadges,
      });
    } else {
      res.status(StatusCodes.CREATED).json({
        msg: "Quiz completed",
        attempt: quizAttempt,
        passed,
      });
    }
  } catch (error) {
    console.error("Error submitting quiz attempt:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error submitting quiz attempt",
      error: error.message,
    });
  }
};

// Get user's quiz attempts
export const getUserQuizAttempts = async (req, res) => {
  try {
    const { userId } = req.user;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [attempts, totalAttempts] = await Promise.all([
      QuizAttempt.find({ userId, isCompleted: true })
        .populate("quizId", "title category difficulty imageUrl")
        .sort({ completedAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      QuizAttempt.countDocuments({ userId, isCompleted: true }),
    ]);

    const totalPages = Math.ceil(totalAttempts / limit);

    res.status(StatusCodes.OK).json({
      attempts,
      currentPage: page,
      totalPages,
      totalAttempts,
      hasMore: page < totalPages,
    });
  } catch (error) {
    console.error("Error fetching user quiz attempts:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error fetching quiz attempts",
      error: error.message,
    });
  }
};

// Get quiz leaderboard
export const getQuizLeaderboard = async (req, res) => {
  try {
    const { id: quizId } = req.params;
    const limit = parseInt(req.query.limit) || 10;

    const leaderboard = await QuizAttempt.aggregate([
      {
        $match: {
          quizId: new mongoose.Types.ObjectId(quizId),
          isCompleted: true,
        },
      },
      {
        $group: {
          _id: "$userId",
          bestScore: { $max: "$totalScore" },
          bestPercentage: { $max: "$percentage" },
          totalAttempts: { $sum: 1 },
          fastestTime: { $min: "$timeSpent" },
          lastAttempt: { $max: "$completedAt" },
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "user",
        },
      },
      {
        $unwind: "$user",
      },
      {
        $project: {
          userId: "$_id",
          username: "$user.name",
          avatar: "$user.avatar",
          bestScore: 1,
          bestPercentage: 1,
          totalAttempts: 1,
          fastestTime: 1,
          lastAttempt: 1,
        },
      },
      {
        $sort: { bestScore: -1, fastestTime: 1 },
      },
      {
        $limit: limit,
      },
    ]);

    res.status(StatusCodes.OK).json({ leaderboard });
  } catch (error) {
    console.error("Error fetching quiz leaderboard:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      msg: "Error fetching leaderboard",
      error: error.message,
    });
  }
};

// Helper function to update quiz statistics
const updateQuizStats = async (quizId) => {
  try {
    const stats = await QuizAttempt.aggregate([
      {
        $match: {
          quizId: new mongoose.Types.ObjectId(quizId),
          isCompleted: true,
        },
      },
      {
        $group: {
          _id: null,
          totalAttempts: { $sum: 1 },
          averageScore: { $avg: "$totalScore" },
          averagePercentage: { $avg: "$percentage" },
          averageTimeSpent: { $avg: "$timeSpent" },
          completedAttempts: { $sum: { $cond: [{ $eq: ["$isCompleted", true] }, 1, 0] } },
        },
      },
    ]);

    if (stats.length > 0) {
      const stat = stats[0];
      await Quiz.findByIdAndUpdate(quizId, {
        "stats.totalAttempts": stat.totalAttempts,
        "stats.averageScore": Math.round(stat.averageScore * 100) / 100,
        "stats.completionRate": Math.round((stat.completedAttempts / stat.totalAttempts) * 100),
        "stats.averageTimeSpent": Math.round(stat.averageTimeSpent),
      });
    }
  } catch (error) {
    console.error("Error updating quiz stats:", error);
  }
};
