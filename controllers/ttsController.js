import { createClient } from '@deepgram/sdk';
import * as dotenv from "dotenv";
dotenv.config();

// Helper function to convert a ReadableStream to a Buffer
async function getAudioBufferFromStream(stream) {
  const reader = stream.getReader();
  const chunks = [];

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    chunks.push(value);
  }

  // Combine all chunks into a single Uint8Array
  const dataArray = chunks.reduce(
    (acc, chunk) => Uint8Array.from([...acc, ...chunk]),
    new Uint8Array(0)
  );

  return Buffer.from(dataArray.buffer);
}

// Initialize the Deepgram client with API key from environment variables
const apiKey = process.env.DEEPGRAM_API_KEY;

// Test endpoint to verify Deepgram SDK installation and API access
export const testDeepgramConnection = async (req, res) => {
  try {
    // Check if API key exists
    if (!apiKey) {
      return res.status(500).json({ error: "Deepgram API key is missing" });
    }

    // Try to initialize the Deepgram client
    try {
      const deepgram = createClient(apiKey);

      // Get SDK version
      const sdkVersion = require('@deepgram/sdk/package.json').version;

      // Try a simple TTS request to verify API access
      try {
        // Simple test text
        const testText = "This is a test of the Deepgram Text-to-Speech API.";

        // Make a small request to test the API using the simplest possible format
        await deepgram.speak.request(
          { text: testText } // Just the text, no options
        );

        // If we get here, the API request was successful
        return res.status(200).json({
          success: true,
          message: "Deepgram client initialized and API access verified successfully",
          sdkVersion,
          apiKeyWorks: true
        });
      } catch (apiError) {
        console.error('Deepgram API test error:', apiError);
        return res.status(500).json({
          error: "Deepgram client initialized but API test failed",
          details: apiError.message,
          stack: apiError.stack,
          sdkVersion
        });
      }
    } catch (error) {
      return res.status(500).json({
        error: "Failed to initialize Deepgram client",
        details: error.message,
        stack: error.stack
      });
    }
  } catch (error) {
    console.error('Error in test endpoint:', error);
    return res.status(500).json({
      error: "Test failed",
      details: error.message,
      stack: error.stack
    });
  }
};

// Handle text-to-speech conversion
export const convertTextToSpeech = async (req, res) => {
  try {
    const { text, voice, model } = req.body;

    // Log environment variables for debugging
    console.log('Environment variables available:', Object.keys(process.env).filter(key => !key.includes('SECRET')));
    console.log('Deepgram API key exists:', Boolean(apiKey));

    if (!text) {
      return res.status(400).json({ error: "Text is required" });
    }

    if (!apiKey) {
      return res.status(500).json({ error: "Deepgram API key is missing. Please add DEEPGRAM_API_KEY to your server's .env file." });
    }

    console.log(`Converting text to speech with voice: ${voice || 'aura'}, model: ${model || 'nova-2'}`);

    try {
      // Initialize the Deepgram client
      const deepgram = createClient(apiKey);

      // Set TTS options according to Deepgram API v3 documentation and example
      // First parameter is the text object, second parameter is the options
      console.log('Sending request to Deepgram with text:', text.substring(0, 20) + '...');

      // Make the API request to Deepgram using the simplest possible format
      console.log('Attempting TTS request with minimal options...');

      const response = await deepgram.speak.request(
        { text }
      );

      console.log('Response received, getting stream...');

      // Get the audio stream
      const stream = await response.getStream();

      if (!stream) {
        throw new Error('Failed to get audio stream from Deepgram');
      }

      // Convert the stream to a buffer
      const audioBuffer = await getAudioBufferFromStream(stream);

      console.log('Received audio response from Deepgram, size:', audioBuffer.byteLength, 'bytes');

      // Set appropriate headers
      res.setHeader('Content-Type', 'audio/mp3');
      res.setHeader('Content-Length', audioBuffer.byteLength);

      // Send the audio data
      res.send(audioBuffer);
    } catch (deepgramError) {
      console.error('Deepgram API error:', deepgramError);
      return res.status(500).json({
        error: "Error communicating with Deepgram API",
        details: deepgramError.message,
        stack: deepgramError.stack
      });
    }
  } catch (error) {
    console.error('Error in TTS controller:', error);
    res.status(500).json({
      error: "Failed to convert text to speech",
      details: error.message,
      stack: error.stack
    });
  }
};
