# Deepgram Text-to-Speech Integration

This document explains how to use the Deepgram Text-to-Speech (TTS) functionality in the SukoonSphere TherapyBot.

## Overview

The TherapyBot now includes text-to-speech capabilities powered by Deepgram's TTS API. This allows users to listen to the therapist's responses, making the application more accessible and providing a more natural interaction experience.

## Features

- Convert therapist messages to speech with a single click
- Toggle automatic TTS for all therapist messages
- High-quality, natural-sounding voices from Deepgram
- Support for different voice options

## Setup

1. **Get a Deepgram API Key**
   - Sign up for a Deepgram account at [https://console.deepgram.com/signup](https://console.deepgram.com/signup)
   - Create a new API key in the Deepgram console
   - Make sure the API key has permissions for the TTS API

2. **Add the API Key to Server Environment Variables**
   - Create or edit the `.env` file in the root directory (server-side)
   - Add your Deepgram API key:
     ```
     DEEPGRAM_API_KEY=your_deepgram_api_key_here
     ```

3. **Restart the Server**
   - After adding the API key, restart the server for the changes to take effect

## Usage

### In the TherapyChat Component

The TherapyChat component now includes TTS functionality:

1. **Listen to Individual Messages**
   - Each therapist message has a speaker icon button
   - Click the speaker icon to listen to that specific message

2. **Toggle Automatic TTS**
   - Use the speaker toggle button in the chat input area
   - When enabled, all new therapist messages will be automatically read aloud
   - When disabled, you can still listen to individual messages by clicking their speaker icons

### Using the TTS Utility Directly

You can use the Deepgram TTS utility in other components:

```javascript
import { textToSpeech, speakWithDeepgram } from '../utils/deepgramTTS';

// Option 1: Get the audio object and control it manually
const handleTTS = async (text) => {
  try {
    const result = await textToSpeech(text, {
      voice: 'aura', // Optional: specify a voice
      model: 'nova-2', // Optional: specify a model
    });

    // Play the audio
    result.play();

    // Later, clean up the audio URL
    result.cleanup();
  } catch (error) {
    console.error('TTS error:', error);
  }
};

// Option 2: Simpler approach that handles playing automatically
const handleSimpleTTS = async (text) => {
  try {
    await speakWithDeepgram(text);
  } catch (error) {
    console.error('TTS error:', error);
  }
};
```

## Available Voices

Deepgram offers several voice options. The default voice is 'aura', but you can specify others:

- 'aura' - Default female voice
- 'stella' - Alternative female voice
- 'nova' - Male voice
- 'onyx' - Alternative male voice

Example:
```javascript
const result = await textToSpeech(text, { voice: 'nova' });
```

## Troubleshooting

1. **No Sound Playing**
   - Check that your Deepgram API key is correct and has TTS permissions
   - Verify that your browser allows audio playback
   - Check the browser console for any errors

2. **API Key Issues**
   - Make sure the API key is correctly set in the server's `.env` file as `DEEPGRAM_API_KEY`
   - Restart the server after changing environment variables

3. **CORS Issues**
   - This implementation uses a server-side proxy to avoid CORS issues
   - If you see CORS errors, make sure you're using the proxy endpoint and not calling Deepgram directly

4. **Server Proxy Issues**
   - Check that the server-side proxy endpoint is working correctly
   - Verify that the server has the Deepgram SDK installed
   - Check server logs for any errors related to the TTS endpoint

5. **Rate Limiting**
   - Deepgram has rate limits on API calls
   - If you encounter rate limiting, consider implementing a queue system for TTS requests

## Testing

Test components are available at:
- `client/src/utils/DeepgramTTSTestV3.jsx` - Use this to test the v3 SDK implementation

You can use these components to verify that the TTS functionality is working correctly.
