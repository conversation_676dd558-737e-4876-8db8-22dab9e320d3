import { body, param, validationResult } from "express-validator";
import { BadRequestError } from "../errors/customErors.js";
import mongoose from "mongoose";

// Helper function to handle validation results
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    throw new BadRequestError(errorMessages.join(', '));
  }
  next();
};

// Validate MongoDB ObjectId
export const validateIdParam = [
  param('id')
    .isMongoId()
    .withMessage('Invalid quiz ID format'),
  handleValidationErrors
];

// Validate quiz creation/update input
export const validateQuizInput = [
  body('title')
    .trim()
    .notEmpty()
    .withMessage('Quiz title is required')
    .isLength({ min: 3, max: 200 })
    .withMessage('Quiz title must be between 3 and 200 characters'),
  
  body('description')
    .trim()
    .notEmpty()
    .withMessage('Quiz description is required')
    .isLength({ min: 10, max: 1000 })
    .withMessage('Quiz description must be between 10 and 1000 characters'),
  
  body('category')
    .trim()
    .notEmpty()
    .withMessage('Quiz category is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Category must be between 2 and 50 characters'),
  
  body('subcategory')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Subcategory must be less than 50 characters'),
  
  body('difficulty')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('Difficulty must be beginner, intermediate, or advanced'),
  
  body('estimatedTime')
    .optional()
    .isInt({ min: 1, max: 300 })
    .withMessage('Estimated time must be between 1 and 300 minutes'),
  
  body('passingScore')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Passing score must be between 0 and 100'),
  
  body('timeLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Time limit must be a positive number'),
  
  body('allowRetakes')
    .optional()
    .isBoolean()
    .withMessage('Allow retakes must be a boolean'),
  
  body('showCorrectAnswers')
    .optional()
    .isBoolean()
    .withMessage('Show correct answers must be a boolean'),
  
  body('randomizeQuestions')
    .optional()
    .isBoolean()
    .withMessage('Randomize questions must be a boolean'),
  
  body('randomizeOptions')
    .optional()
    .isBoolean()
    .withMessage('Randomize options must be a boolean'),
  
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  
  body('tags.*')
    .optional()
    .trim()
    .isLength({ min: 1, max: 30 })
    .withMessage('Each tag must be between 1 and 30 characters'),
  
  handleValidationErrors
];

// Validate quiz questions
export const validateQuizQuestions = [
  body('questions')
    .isArray({ min: 1 })
    .withMessage('Quiz must have at least one question'),
  
  body('questions.*.text')
    .trim()
    .notEmpty()
    .withMessage('Question text is required')
    .isLength({ min: 5, max: 500 })
    .withMessage('Question text must be between 5 and 500 characters'),
  
  body('questions.*.type')
    .optional()
    .isIn(['multiple-choice', 'true-false', 'rating-scale', 'text-input'])
    .withMessage('Invalid question type'),
  
  body('questions.*.options')
    .isArray({ min: 2 })
    .withMessage('Question must have at least 2 options'),
  
  body('questions.*.options.*.label')
    .trim()
    .notEmpty()
    .withMessage('Option label is required')
    .isLength({ min: 1, max: 200 })
    .withMessage('Option label must be between 1 and 200 characters'),
  
  body('questions.*.options.*.score')
    .isNumeric()
    .withMessage('Option score must be a number'),
  
  body('questions.*.points')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Question points must be a positive integer'),
  
  body('questions.*.timeLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Question time limit must be a positive integer'),
  
  body('questions.*.difficulty')
    .optional()
    .isIn(['easy', 'medium', 'hard'])
    .withMessage('Question difficulty must be easy, medium, or hard'),
  
  handleValidationErrors
];

// Validate quiz attempt submission
export const validateQuizAttempt = [
  body('responses')
    .isArray({ min: 1 })
    .withMessage('Quiz responses are required'),
  
  body('responses.*.questionId')
    .trim()
    .notEmpty()
    .withMessage('Question ID is required for each response'),
  
  body('responses.*.selectedOption')
    .trim()
    .notEmpty()
    .withMessage('Selected option is required for each response'),
  
  body('timeSpent')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Time spent must be a non-negative integer'),
  
  handleValidationErrors
];

// Validate JSON quiz upload
export const validateJsonQuizUpload = (req, res, next) => {
  try {
    if (!req.file) {
      throw new BadRequestError('JSON file is required');
    }
    
    if (req.file.mimetype !== 'application/json') {
      throw new BadRequestError('File must be a valid JSON file');
    }
    
    // Parse the JSON content
    const jsonContent = JSON.parse(req.file.buffer.toString());
    
    // Validate basic structure
    if (!jsonContent.title || !jsonContent.description || !jsonContent.category) {
      throw new BadRequestError('JSON must contain title, description, and category');
    }
    
    if (!jsonContent.questions || !Array.isArray(jsonContent.questions) || jsonContent.questions.length === 0) {
      throw new BadRequestError('JSON must contain at least one question');
    }
    
    // Validate each question
    jsonContent.questions.forEach((question, index) => {
      if (!question.text || !question.options || !Array.isArray(question.options) || question.options.length < 2) {
        throw new BadRequestError(`Question ${index + 1} must have text and at least 2 options`);
      }
      
      question.options.forEach((option, optionIndex) => {
        if (!option.label || typeof option.score !== 'number') {
          throw new BadRequestError(`Question ${index + 1}, option ${optionIndex + 1} must have label and score`);
        }
      });
    });
    
    // Attach parsed content to request
    req.quizData = jsonContent;
    next();
  } catch (error) {
    if (error instanceof SyntaxError) {
      throw new BadRequestError('Invalid JSON format');
    }
    throw error;
  }
};
