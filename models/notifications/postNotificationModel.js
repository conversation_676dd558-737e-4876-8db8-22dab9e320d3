import mongoose from "mongoose";

const postNotificationSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    postId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Post',
    },
    questionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question',
    },
    answerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Answer',
    },
    answerCommentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'AnswerComment',
    },
    answerReplyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'AnswerReply',
    },
    postCommentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment',
    },
    postReplyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Reply',
    },
    articleId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Article',
    },
    articleCommentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment',
    },
    articleReplyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Reply',
    },
    chatId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Chat',
    },
    chatDisabled: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      enum: [
        // Basic types
        'like', 'comment', 'reply', 'commentLiked', 'replyLiked',
        "answered", "answerLiked", "answerCommentLiked", "answerCommentReplyLiked", "answerComment", "answerReply",
        "articleLiked", "articleCommentLiked", "articleCommentReplyLiked", "articleComment", "articleReply",
        "follow", "unfollow", "requestChat", "roomInvitation", "roomJoinRequest", "roomJoinRequestApproved", "roomJoinRequestRejected",
        'personalStoryComment',
        'personalStoryCommentReply',
        'personalStoryCommentLiked',
        'personalStoryCommentReplyLiked',
        'personalStoryLiked',
        'personalStoryReply',

        "videoComment",
        "videoReply",
        // New reaction types
        'reaction',  // Generic reaction notification
        'reactionHeart', 'reactionHaha', 'reactionWow', 'reactionSupport',
        'reactionRelate', 'reactionAgree', 'reactionSad', 'reactionAngry', 'reactionInsightful',

        // Content-specific reaction types
        'commentReaction', 'replyReaction', 'articleReaction', 'videoReaction',
        'answerReaction', 'personalStoryReaction'
      ],
      required: true,
    },
    message: {
      type: String,
      required: true,
    },

    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    seen: {
      type: Boolean,
      default: false,
    }
  },
  {
    timestamps: true,
  }
);

const PostNotification = mongoose.model("PostNotification", postNotificationSchema);

export default PostNotification; // Ensure this line is present