import mongoose from "mongoose";

// Schema for individual question responses
const QuestionResponseSchema = new mongoose.Schema({
  questionId: {
    type: String,
    required: true,
  },
  selectedOption: {
    type: String,
    required: true,
  },
  isCorrect: {
    type: Boolean,
    required: true,
  },
  pointsEarned: {
    type: Number,
    default: 0,
  },
  timeSpent: {
    type: Number, // in seconds
    default: 0,
  },
  answeredAt: {
    type: Date,
    default: Date.now,
  }
});

// Main quiz attempt schema
const QuizAttemptSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    quizId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Quiz",
      required: true,
    },
    responses: [QuestionResponseSchema],
    totalScore: {
      type: Number,
      default: 0,
    },
    maxPossibleScore: {
      type: Number,
      required: true,
    },
    percentage: {
      type: Number,
      default: 0,
    },
    passed: {
      type: Boolean,
      default: false,
    },
    timeSpent: {
      type: Number, // in seconds
      default: 0,
    },
    startedAt: {
      type: Date,
      default: Date.now,
    },
    completedAt: {
      type: Date,
    },
    isCompleted: {
      type: Boolean,
      default: false,
    },
    attemptNumber: {
      type: Number,
      default: 1,
    },
    feedback: {
      type: String,
      default: "",
    },
    assessmentResult: {
      title: String,
      description: String,
      minScore: Number,
      maxScore: Number,
    }
  },
  { timestamps: true }
);

// Pre-save hook to calculate percentage and determine pass/fail
QuizAttemptSchema.pre("save", function (next) {
  if (this.maxPossibleScore > 0) {
    this.percentage = Math.round((this.totalScore / this.maxPossibleScore) * 100);
  }
  
  // Determine if user passed (this will be set based on quiz's passing score)
  // The passing logic will be handled in the controller
  
  next();
});

// Index for better query performance
QuizAttemptSchema.index({ userId: 1, quizId: 1 });
QuizAttemptSchema.index({ quizId: 1, isCompleted: 1 });
QuizAttemptSchema.index({ userId: 1, completedAt: -1 });

const QuizAttempt = mongoose.model("QuizAttempt", QuizAttemptSchema);

export default QuizAttempt;
