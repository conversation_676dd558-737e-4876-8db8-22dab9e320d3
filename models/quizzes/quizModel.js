import mongoose from "mongoose";

// Schema for quiz options
const QuizOptionSchema = new mongoose.Schema({
  label: {
    type: String,
    required: true,
  },
  value: {
    type: String,
    required: true,
  },
  score: {
    type: Number,
    required: true,
  },
  isCorrect: {
    type: Boolean,
    default: false,
  },
  explanation: {
    type: String,
    default: "",
  }
});

// Schema for quiz questions
const QuizQuestionSchema = new mongoose.Schema({
  questionId: {
    type: String,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: ['multiple-choice', 'true-false', 'rating-scale', 'text-input'],
    default: 'multiple-choice',
  },
  options: [QuizOptionSchema],
  order: {
    type: Number,
    required: true,
  },
  points: {
    type: Number,
    default: 1,
  },
  timeLimit: {
    type: Number, // in seconds
    default: null,
  },
  explanation: {
    type: String,
    default: "",
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium',
  },
  tags: [{
    type: String,
  }]
});

// Schema for assessment ranges
const AssessmentRangeSchema = new mongoose.Schema({
  minScore: {
    type: Number,
    required: true,
  },
  maxScore: {
    type: Number,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  }
});

// Schema for quiz statistics
const QuizStatsSchema = new mongoose.Schema({
  totalAttempts: {
    type: Number,
    default: 0,
  },
  averageScore: {
    type: Number,
    default: 0,
  },
  completionRate: {
    type: Number,
    default: 0,
  },
  averageTimeSpent: {
    type: Number, // in seconds
    default: 0,
  }
});

// Main quiz schema
const QuizSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    subcategory: {
      type: String,
      default: "",
    },
    questions: [QuizQuestionSchema],
    assessments: [AssessmentRangeSchema],
    isActive: {
      type: Boolean,
      default: true,
    },
    isPublished: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    imageUrl: {
      type: String,
    },
    difficulty: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced'],
      default: 'intermediate',
    },
    estimatedTime: {
      type: Number, // in minutes
      default: 10,
    },
    totalPoints: {
      type: Number,
      default: 0,
    },
    passingScore: {
      type: Number,
      default: 60, // percentage
    },
    allowRetakes: {
      type: Boolean,
      default: true,
    },
    showCorrectAnswers: {
      type: Boolean,
      default: true,
    },
    randomizeQuestions: {
      type: Boolean,
      default: false,
    },
    randomizeOptions: {
      type: Boolean,
      default: false,
    },
    timeLimit: {
      type: Number, // in minutes
      default: null,
    },
    tags: [{
      type: String,
    }],
    stats: {
      type: QuizStatsSchema,
      default: () => ({}),
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    }
  }
);

// Pre-save hook to update updatedAt timestamp and calculate total points
QuizSchema.pre("save", function (next) {
  this.updatedAt = new Date();

  // Calculate total points from all questions
  if (this.questions && this.questions.length > 0) {
    this.totalPoints = this.questions.reduce((total, question) => {
      return total + (question.points || 1);
    }, 0);
  }

  next();
});

// Index for better query performance
QuizSchema.index({ category: 1, isActive: 1, isPublished: 1 });
QuizSchema.index({ createdBy: 1 });
QuizSchema.index({ tags: 1 });
QuizSchema.index({ difficulty: 1 });

const Quiz = mongoose.model("Quiz", QuizSchema);

export default Quiz;
