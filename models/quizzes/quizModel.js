import mongoose from "mongoose";

// Schema for quiz options
const QuizOptionSchema = new mongoose.Schema({
  label: {
    type: String,
    required: true,
  },
  value: {
    type: String,
    required: true,
  },
  score: {
    type: Number,
    required: true,
  }
});

// Schema for quiz questions
const QuizQuestionSchema = new mongoose.Schema({
  questionId: {
    type: String,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
  options: [QuizOptionSchema],
  order: {
    type: Number,
    required: true,
  }
});

// Schema for assessment ranges
const AssessmentRangeSchema = new mongoose.Schema({
  minScore: {
    type: Number,
    required: true,
  },
  maxScore: {
    type: Number,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  }
});

// Main quiz schema
const QuizSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    questions: [QuizQuestionSchema],
    assessments: [AssessmentRangeSchema],
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    imageUrl: {
      type: String,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    }
  }
);

// Pre-save hook to update updatedAt timestamp
QuizSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

const Quiz = mongoose.model("Quiz", QuizSchema);

export default Quiz;
