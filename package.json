{"name": "sukoonsphere", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "setup-project": "npm i && cd client && npm i && cd .. && npm run dev", "server": "nodemon server", "client": "cd client && npm run dev", "dev": "concurrently --kill-others-on-fail \"npm run server \" \"npm run client\""}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^8.0.1"}, "dependencies": {"@deepgram/sdk": "^3.12.1", "@google/generative-ai": "^0.24.0", "@tanstack/react-query": "^5.62.2", "aos": "^2.3.4", "axios": "^1.7.4", "bcryptjs": "^2.4.3", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.8", "cloudinary": "^2.0.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "datauri": "^4.1.0", "dayjs": "^1.11.7", "dotenv": "^16.0.3", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-fileupload": "^1.5.1", "express-validator": "^7.0.1", "framer-motion": "^12.6.5", "highcharts": "^12.1.2", "highcharts-react-official": "^3.2.1", "http-status-codes": "^2.2.0", "jodit-react": "^4.1.2", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nanoid": "^4.0.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.11", "nodemon": "^3.0.3", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-twitter": "^1.0.4", "pdfjs-dist": "^4.9.155", "react-chartjs-2": "^5.3.0", "react-intersection-observer": "^9.13.1", "react-quill": "^2.0.0", "react-to-print": "^3.0.5", "socket.io": "^4.8.1", "ws": "^8.18.1"}}