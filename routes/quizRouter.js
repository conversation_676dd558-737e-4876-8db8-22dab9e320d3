import { Router } from "express";
import multer from "multer";
import mongoose from "mongoose";
import Quiz from "../models/quizzes/quizModel.js";
import QuizAttempt from "../models/quizzes/quizAttemptModel.js";
import {
  createQuiz,
  createQuizFromJson,
  getAllQuizzes,
  getQuizById,
  updateQuiz,
  deleteQuiz,
  getUserQuizzes,
  submitQuizAttempt,
  getUserQuizAttempts,
  getQuizLeaderboard,
} from "../controllers/quizController.js";
import {
  authenticateUser,
  optionalAuthenticateUser,
  authorizeAdminOrContributor,
} from "../middleware/authMiddleware.js";
import {
  validateIdParam,
  validateQuizInput,
  validateQuizQuestions,
  validateQuizAttempt,
  validateJsonQuizUpload,
} from "../middleware/quizValidationMiddleware.js";
import { trackContentView } from "../middleware/activityTrackingMiddleware.js";

const router = Router();

// Configure multer for JSON file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/json') {
      cb(null, true);
    } else {
      cb(new Error('Only JSON files are allowed'), false);
    }
  },
});

// Public routes
router.get("/", getAllQuizzes);
router.get("/:id", optionalAuthenticateUser, validateIdParam, trackContentView, getQuizById);
router.get("/:id/leaderboard", validateIdParam, getQuizLeaderboard);

// Protected routes - require authentication
router.post(
  "/:id/attempt",
  authenticateUser,
  validateIdParam,
  validateQuizAttempt,
  submitQuizAttempt
);
router.get("/user/attempts", authenticateUser, getUserQuizAttempts);
router.get("/user/:userId", validateIdParam, getUserQuizzes);

// Admin/Contributor only routes - quiz management
router.post(
  "/",
  authenticateUser,
  authorizeAdminOrContributor,
  validateQuizInput,
  validateQuizQuestions,
  createQuiz
);

router.post(
  "/upload-json",
  authenticateUser,
  authorizeAdminOrContributor,
  upload.single('quizFile'),
  validateJsonQuizUpload,
  createQuizFromJson
);

router.put(
  "/:id",
  authenticateUser,
  authorizeAdminOrContributor,
  validateIdParam,
  validateQuizInput,
  validateQuizQuestions,
  updateQuiz
);

router.delete(
  "/:id",
  authenticateUser,
  authorizeAdminOrContributor,
  validateIdParam,
  deleteQuiz
);

// Publish/unpublish quiz (admin/contributor only)
router.patch(
  "/:id/publish",
  authenticateUser,
  authorizeAdminOrContributor,
  validateIdParam,
  async (req, res) => {
    try {
      const { id } = req.params;
      const { isPublished } = req.body;
      
      const quiz = await Quiz.findByIdAndUpdate(
        id,
        { isPublished: Boolean(isPublished) },
        { new: true }
      );
      
      if (!quiz) {
        return res.status(404).json({ msg: "Quiz not found" });
      }
      
      res.json({
        msg: `Quiz ${isPublished ? 'published' : 'unpublished'} successfully`,
        quiz,
      });
    } catch (error) {
      res.status(500).json({
        msg: "Error updating quiz publication status",
        error: error.message,
      });
    }
  }
);

// Get quiz categories (public)
router.get("/meta/categories", async (req, res) => {
  try {
    const categories = await Quiz.distinct("category", { isActive: true, isPublished: true });
    res.json({ categories });
  } catch (error) {
    res.status(500).json({
      msg: "Error fetching categories",
      error: error.message,
    });
  }
});

// Get quiz statistics (admin/contributor only)
router.get(
  "/:id/stats",
  authenticateUser,
  authorizeAdminOrContributor,
  validateIdParam,
  async (req, res) => {
    try {
      const { id } = req.params;
      
      const quiz = await Quiz.findById(id).select("stats title");
      
      if (!quiz) {
        return res.status(404).json({ msg: "Quiz not found" });
      }
      
      // Get detailed statistics
      const detailedStats = await QuizAttempt.aggregate([
        {
          $match: {
            quizId: new mongoose.Types.ObjectId(id),
            isCompleted: true,
          },
        },
        {
          $group: {
            _id: null,
            totalAttempts: { $sum: 1 },
            uniqueUsers: { $addToSet: "$userId" },
            averageScore: { $avg: "$totalScore" },
            highestScore: { $max: "$totalScore" },
            lowestScore: { $min: "$totalScore" },
            averageTime: { $avg: "$timeSpent" },
            passedAttempts: {
              $sum: { $cond: [{ $eq: ["$passed", true] }, 1, 0] },
            },
          },
        },
        {
          $project: {
            totalAttempts: 1,
            uniqueUsers: { $size: "$uniqueUsers" },
            averageScore: { $round: ["$averageScore", 2] },
            highestScore: 1,
            lowestScore: 1,
            averageTime: { $round: ["$averageTime", 0] },
            passRate: {
              $round: [
                { $multiply: [{ $divide: ["$passedAttempts", "$totalAttempts"] }, 100] },
                2,
              ],
            },
          },
        },
      ]);
      
      res.json({
        quiz: {
          id: quiz._id,
          title: quiz.title,
          basicStats: quiz.stats,
          detailedStats: detailedStats[0] || {},
        },
      });
    } catch (error) {
      res.status(500).json({
        msg: "Error fetching quiz statistics",
        error: error.message,
      });
    }
  }
);

export default router;
