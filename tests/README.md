# SukoonSphere Email Testing

This directory contains test scripts for various SukoonSphere functionalities.

## Email Test Script

The `emailTest.js` script allows you to test the email sending functionality without going through the entire registration or password reset flow.

### Prerequisites

Make sure you have set up your environment variables in the `.env` file at the root of the project. For email testing, the following variables are important:

```
EMAIL_HOST=smtp-relay.brevo.com
EMAIL_PORT=587
EMAIL_USER=your_email_user
EMAIL_PASS=your_email_password
EMAIL_FROM=<EMAIL>
```

### Running the Tests

You can run the email tests using the following command from the project root:

```bash
node tests/emailTest.js [test-type] [email-address]
```

#### Parameters:

- `test-type` (optional): Specifies which email test to run. Options are:
  - `verification`: Test only the verification email
  - `reset`: Test only the password reset email
  - `contributor`: Test only the contributor key email
  - `all` or omitted: Run all email tests (default)

- `email-address` (optional): The email address to send the test emails to. If not provided, it defaults to `<EMAIL>`.

#### Examples:

Run all email tests with the default email address:
```bash
node tests/emailTest.js
```

Run only the verification email test:
```bash
node tests/emailTest.js verification
```

Run the password reset email test with a specific email address:
```bash
node tests/emailTest.<NAME_EMAIL>
```

### Troubleshooting

If you encounter issues with the email tests:

1. Check your `.env` file to ensure all email configuration variables are set correctly
2. Verify that your email service provider (e.g., Brevo) is properly configured
3. Check the console output for specific error messages
4. Ensure your email provider allows sending from the specified "from" address
5. Check if your email provider has rate limits or requires additional configuration

### Note

These tests send actual emails. Be mindful of how frequently you run them to avoid hitting rate limits with your email service provider.
