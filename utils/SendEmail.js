import nodemailer from "nodemailer";

const SendEmail = async ({ to, subject, html }) => {
  try {
    // Use environment variables for email configuration or fallback to defaults
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || "smtp-relay.brevo.com",
      port: parseInt(process.env.EMAIL_PORT || "587"),
      auth: {
        user: process.env.EMAIL_USER || "<EMAIL>",
        pass: process.env.EMAIL_PASS || "7ZYwMhrzsjybBWgK",
      },
    });


    // Define the mail options
    const mailOptions = {
      from: process.env.EMAIL_FROM || '"Sukoon Sphere" <<EMAIL>>',
      to,
      subject,
      html,
    };

    // Send the email
    const info = await transporter.sendMail(mailOptions);

  } catch (error) {
    console.error("Error sending email:", error);
    throw error; // Propagate the error for further handling
  }
};

export default SendEmail;
