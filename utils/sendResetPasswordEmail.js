import SendEmail from "./SendEmail.js";

const sendResetPasswordEmail = async ({ name, email, token, origin }) => {
  const resetEmail = `https://sukoonsphere.org/user/reset-password?token=${token}&email=${email}`;

  const message = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2c3e50; margin-bottom: 20px;">Password Reset Request</h2>
      <p style="color: #34495e; line-height: 1.6;">
        Dear ${name},
      </p>
      <p style="color: #34495e; line-height: 1.6;">
        We received a request to reset your password for your Sukoon Sphere account. If you didn't make this request, you can safely ignore this email.
      </p>
      <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p style="color: #2c3e50; font-weight: bold; margin-bottom: 10px;">To reset your password, click the button below:</p>
        <a href="${resetEmail}" style="display: inline-block; background-color: #4ecdc4; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; font-weight: bold;">Reset Password</a>
        <p style="color: #666; font-size: 12px; margin-top: 12px;">If the button doesn't work, you can copy and paste this link into your browser: <br><a href="${resetEmail}" style="color: #3498db;">${resetEmail}</a></p>
      </div>
      <p style="color: #34495e; line-height: 1.6;">
        This password reset link will expire in 10 minutes for security reasons.
      </p>
      <p style="color: #34495e; line-height: 1.6;">
        Best regards,<br>
        The Sukoon Sphere Team
      </p>
    </div>
  `;

  return SendEmail({
    to: email,
    subject: "Sukoon Sphere - Password Reset Request",
    html: message,
  });
};
export default sendResetPasswordEmail;
