import SendEmail from "./SendEmail.js";

const sendVerificationEmail = async ({
  name,
  email,
  verificationToken,
  origin,
}) => {
  const verifyEmail = `https://sukoonsphere.org/user/verify-email?token=${verificationToken}&email=${email}`;

  const message = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2c3e50; margin-bottom: 20px;">Welcome to Sukoon Sphere! 🌿</h2>
      <p style="color: #34495e; line-height: 1.6;">
        Dear ${name},
      </p>
      <p style="color: #34495e; line-height: 1.6;">
        Thank you for joining Sukoon Sphere, your sanctuary for mental well-being. We're excited to have you as part of our community!
      </p>
      <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p style="color: #2c3e50; font-weight: bold; margin-bottom: 10px;">Please verify your email address:</p>
        <a href="${verifyEmail}" style="display: inline-block; background-color: #4ecdc4; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; font-weight: bold;">Verify Email Address</a>
        <p style="color: #666; font-size: 12px; margin-top: 12px;">If the button doesn't work, you can copy and paste this link into your browser: <br><a href="${verifyEmail}" style="color: #3498db;">${verifyEmail}</a></p>
      </div>
      <p style="color: #34495e; line-height: 1.6;">
        At Sukoon Sphere, you'll find:
        <ul style="color: #34495e; line-height: 1.6;">
          <li>A supportive community focused on mental health</li>
          <li>Resources and tools for your well-being journey</li>
          <li>Expert advice and shared experiences</li>
        </ul>
      </p>
      <p style="color: #34495e; line-height: 1.6;">
        We're glad you've joined us on this journey toward better mental health and well-being.
      </p>
      <p style="color: #34495e; line-height: 1.6;">
        Best regards,<br>
        The Sukoon Sphere Team
      </p>
    </div>
  `;

  return SendEmail({
    to: email,
    subject: "Welcome to Sukoon Sphere - Please Verify Your Email",
    html: message,
  });
};
export default sendVerificationEmail